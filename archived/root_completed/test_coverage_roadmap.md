# Test Coverage Roadmap

## Current Status
- **Overall Coverage**: 58% (3,528/8,377 lines uncovered)
- **Failing Tests**: 93
- **Missing Tests**: ~50-60 test files

## Priority 1: Critical Untested Modules (1,628 lines)

### 1. Visualization Module (391 lines)
**Files**: `model_diagnostics.py`, `price_dynamics.py`
**Why Critical**: Output generation for analysis results
**New Tests Needed**:
- `test_model_diagnostics.py` (15-20 tests)
- `test_price_dynamics.py` (10-15 tests)

### 2. Econometric Backbone (188 lines)
**File**: `models/econometric_backbone.py`
**Why Critical**: Base functionality for all models
**New Tests Needed**:
- `test_econometric_backbone.py` (20-25 tests)

### 3. Model Comparison (472 lines)
**File**: `models/model_comparison.py`
**Why Critical**: Cross-model validation
**New Tests Needed**:
- `test_model_comparison.py` (25-30 tests)

### 4. WorldBank Diagnostics (278 lines)
**File**: `diagnostics/worldbank_diagnostics.py`
**Why Critical**: Standard econometric tests
**New Tests Needed**:
- `test_worldbank_diagnostics.py` (20-25 tests)

### 5. Performance & Security Utils (299 lines)
**Files**: `utils/performance.py`, `utils/security.py`
**Why Critical**: Infrastructure reliability
**New Tests Needed**:
- `test_performance.py` (10-15 tests)
- `test_security.py` (10-15 tests)

## Priority 2: Increase Coverage for Partial Tests

### Low Coverage Critical Models
1. **threshold_vecm.py** (39% → 90%)
   - Add 15-20 edge case tests
   - Test regime switching thoroughly
   
2. **factor_models.py** (47% → 90%)
   - Add dynamic factor tests
   - Test structural break detection

3. **model_migration.py** (48% → 85%)
   - Test rollback scenarios
   - Test version compatibility

## Priority 3: Edge Cases & Integration

### Missing Test Types
1. **Error Handling Tests**
   - Malformed data inputs
   - Network failures (HDX client)
   - File system errors
   
2. **Performance Tests**
   - Large dataset handling (>1M rows)
   - Memory usage validation
   - Concurrent access

3. **Integration Tests**
   - Full pipeline tests
   - Cross-module data flow
   - Real data scenarios

## Implementation Strategy

### Phase 1: Quick Wins (Day 1)
- Generate test stubs for all untested modules
- Add basic happy-path tests
- Target: 58% → 70% coverage

### Phase 2: Critical Paths (Days 2-3)
- Complete visualization tests
- Full econometric backbone coverage
- Target: 70% → 85% coverage

### Phase 3: Edge Cases (Days 4-5)
- Error scenarios
- Performance boundaries
- Integration scenarios
- Target: 85% → 95% coverage

## Test Generation Commands

```bash
# Terminal 6: Test Coverage Enhancement
cd /Users/<USER>/Documents/GitHub/yemen-market-integration

# Generate visualization tests
cat > tests/unit/test_model_diagnostics.py << 'EOF'
"""Tests for model diagnostics visualization."""
import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch
import matplotlib.pyplot as plt

from yemen_market.visualization.model_diagnostics import (
    # Import classes/functions once we inspect the module
)

class TestModelDiagnostics:
    """Test model diagnostic visualizations."""
    
    @pytest.fixture
    def sample_residuals(self):
        """Create sample residuals for testing."""
        np.random.seed(42)
        return pd.Series(np.random.normal(0, 1, 100))
    
    # Add 15-20 tests here
EOF

# Generate performance tests
cat > tests/unit/test_performance.py << 'EOF'
"""Tests for performance monitoring utilities."""
import pytest
from yemen_market.utils.performance import (
    # Import after inspection
)

class TestPerformanceUtils:
    """Test performance monitoring."""
    
    # Add 10-15 tests
EOF
```

## Metrics to Track

### Coverage Targets by Module Type
- **Core Models**: 95% minimum
- **Data Processing**: 90% minimum
- **Utilities**: 85% minimum
- **Visualization**: 80% minimum

### Test Quality Metrics
- **Assertion Density**: >2 assertions per test
- **Mock Usage**: <30% of tests use mocks
- **Edge Case Coverage**: >25% of tests
- **Performance Tests**: >5% of test suite

## Parallel Terminal Assignment

While terminals 1-5 fix failing tests, you could:

**Terminal 6**: Focus on test coverage
```bash
claude "Create comprehensive tests for all untested modules in test_coverage_roadmap.md. Start with visualization and econometric_backbone. Aim for 90%+ coverage."
```

## Expected Timeline
- **Day 1**: Fix 93 failing tests (Terminals 1-5) + Add 30 new test files (Terminal 6)
- **Day 2**: Complete test fixes + Add remaining 20 test files
- **Day 3**: Edge cases + Integration tests
- **Final Coverage**: 95%+ with all tests passing
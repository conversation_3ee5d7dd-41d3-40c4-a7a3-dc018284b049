# Critical Econometric Implementation Gaps

## 1. **WorldBank Diagnostics (0% coverage) - CRITICAL**
Missing production implementation for:
- Weak instrument tests (<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>)
- Endogeneity tests (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)
- Overidentification tests (<PERSON><PERSON><PERSON><PERSON>)
- Serial correlation tests for panel data (<PERSON><PERSON><PERSON>, Arellano-Bond)
- Cross-sectional dependence tests (Pesaran CD)

## 2. **Econometric Backbone (0% coverage) - CRITICAL**
Missing core functionality:
- Generalized Method of Moments (GMM) estimators
- System GMM for dynamic panels
- Arellano-Bond/Blundell-Bond estimators
- Driscoll-Kraay standard errors implementation
- Spatial HAC standard errors

## 3. **Model Comparison Framework (6% coverage)**
Missing implementation:
- Model selection criteria (AIC, BIC, HQIC)
- Out-of-sample forecasting evaluation
- Diebold-Mariano test for forecast comparison
- Model averaging techniques
- Cross-validation for panel data

## 4. **Threshold VECM (39% coverage)**
Missing critical methods:
- <PERSON> (1999) threshold test implementation
- Bootstrap p-values for threshold significance
- Regime-specific impulse responses
- Generalized impulse response functions
- Threshold cointegration tests (Gonzalo-Pitarakis)

## 5. **Factor Models (47% coverage)**
Missing implementation:
- <PERSON><PERSON><PERSON> (2002) information criteria for factor selection
- Principal components for unbalanced panels
- Time-varying factor loadings
- Structural break tests in factor models
- Factor-augmented VAR (FAVAR)

## Required Implementations:

### A. Diagnostic Tests Module
```python
# src/yemen_market/diagnostics/worldbank_diagnostics.py
class PanelDiagnostics:
    def wooldridge_serial_correlation_test(self, residuals, panel_structure):
        """Wooldridge test for serial correlation in panel data"""
        # IMPLEMENT: First-differenced regression test
        
    def pesaran_cd_test(self, residuals, panel_structure):
        """Pesaran cross-sectional dependence test"""
        # IMPLEMENT: Cross-sectional correlation test
        
    def kleibergen_paap_weak_instruments(self, instruments, endogenous, exogenous):
        """Kleibergen-Paap rk statistic for weak instruments"""
        # IMPLEMENT: Robust to heteroskedasticity and clustering
```

### B. GMM Estimators
```python
# src/yemen_market/models/econometric_backbone.py
class SystemGMM:
    def arellano_bond_estimator(self, data, lags, instruments):
        """First-difference GMM estimator"""
        # IMPLEMENT: Dynamic panel GMM
        
    def blundell_bond_estimator(self, data, lags, instruments):
        """System GMM estimator"""
        # IMPLEMENT: Combines levels and differences
```

### C. Threshold Testing
```python
# src/yemen_market/models/three_tier/tier2_commodity/threshold_vecm.py
def hansen_threshold_test(self, data, threshold_var, n_bootstrap=1000):
    """Hansen (1999) threshold effect test"""
    # IMPLEMENT: Sup-Wald test with bootstrap p-values
    
def gonzalo_pitarakis_test(self, price_data, threshold_var):
    """Threshold cointegration test"""
    # IMPLEMENT: Tests for threshold effects in cointegration
```

## Priority Order for Implementation:

1. **Diagnostic Tests** (Days 1-2)
   - Essential for validating econometric assumptions
   - Required for paper credibility

2. **GMM Estimators** (Days 3-4)
   - Core methodology for dynamic panels
   - Handles endogeneity concerns

3. **Threshold Tests** (Day 5)
   - Critical for conflict analysis
   - Validates regime-switching approach

4. **Model Comparison** (Day 6)
   - Validates model selection
   - Provides robustness checks

## These are NOT test stubs - these are missing core econometric methods that must be implemented for the methodology to be valid.
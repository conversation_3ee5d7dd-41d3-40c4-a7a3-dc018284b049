"""
Comprehensive tests for ModelComparison class.

Tests all methods and functionality with World Bank macroeconomist methodological rigor.
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import tempfile
import matplotlib.pyplot as plt

from yemen_market.models.model_comparison import (
    ModelComparison,
    run_model_comparison,
    ComparisonSuite
)
from yemen_market.models.three_tier.core.base_model import BaseThreeTierModel
from yemen_market.models.three_tier.core.results_container import ResultsContainer


class MockModel(BaseThreeTierModel):
    """Mock model for testing."""

    def __init__(self, name: str = "mock", is_fitted: bool = True, **kwargs):
        super().__init__()
        self.name = name
        self.is_fitted = is_fitted  # Override the base class attribute
        self.mock_results = kwargs.get('results', {})
        self.mock_ic = kwargs.get('ic', {'AIC': 100, 'BIC': 110})
        self.mock_diagnostics = kwargs.get('diagnostics', {'ljung_box': 0.05})
        self.mock_coefficients = kwargs.get('coefficients', {'alpha': 0.5})

        # Add vecm_results attribute for compatibility
        self.vecm_results = Mock()
        self.vecm_results.alpha = np.array([0.5])  # Default alpha value
        self.vecm_results.regime_assignment = np.array([0, 1, 0, 1, 0])

    def fit(self, data: pd.DataFrame, **kwargs) -> ResultsContainer:
        self.is_fitted = True
        return ResultsContainer(
            commodity='test',
            model_type=self.name,
            results=self.mock_results
        )

    def predict(self, steps: int = 1, **kwargs) -> pd.DataFrame:
        """Mock prediction."""
        dates = pd.date_range('2023-01-01', periods=steps, freq='M')
        return pd.DataFrame({
            'prediction': np.random.normal(100, 10, steps)
        }, index=dates)

    def get_information_criteria(self) -> dict:
        """Mock information criteria."""
        return self.mock_ic.copy()

    def get_diagnostics(self) -> dict:
        """Mock diagnostics."""
        return self.mock_diagnostics.copy()

    def get_coefficients(self, param_name: str = 'alpha') -> dict:
        """Mock coefficients."""
        return {param_name: self.mock_coefficients.get(param_name, 0.5)}

    def forecast_evaluation(self, test_data: pd.DataFrame, metrics: list) -> dict:
        """Mock forecast evaluation."""
        return {metric: np.random.uniform(0.1, 1.0) for metric in metrics}

    def validate_data(self, data: pd.DataFrame) -> tuple[bool, list[str]]:
        """Mock data validation."""
        if data is None or data.empty:
            return False, ['Data is empty or None']
        return True, []


class TestModelComparison:
    """Test ModelComparison class."""

    @pytest.fixture
    def mock_models(self):
        """Create mock models for testing."""
        model1 = MockModel(
            name="Model1",
            ic={'AIC': 100, 'BIC': 110},
            diagnostics={'ljung_box': 0.05, 'arch_test': 0.03},
            coefficients={'alpha': 0.5, 'beta': 0.3}
        )

        model2 = MockModel(
            name="Model2",
            ic={'AIC': 95, 'BIC': 105},
            diagnostics={'ljung_box': 0.02, 'arch_test': 0.01},
            coefficients={'alpha': 0.6, 'beta': 0.4}
        )

        model3 = MockModel(
            name="Model3",
            ic={'AIC': 105, 'BIC': 115},
            diagnostics={'ljung_box': 0.08, 'arch_test': 0.06},
            coefficients={'alpha': 0.4, 'beta': 0.2}
        )

        return {'Model1': model1, 'Model2': model2, 'Model3': model3}

    @pytest.fixture
    def comparison(self, mock_models):
        """Create ModelComparison instance."""
        return ModelComparison(mock_models)

    @pytest.fixture
    def empty_comparison(self):
        """Create empty ModelComparison instance."""
        return ModelComparison()

    @pytest.fixture
    def sample_test_data(self):
        """Create sample test data."""
        dates = pd.date_range('2023-01-01', periods=12, freq='M')
        return pd.DataFrame({
            'price': np.random.normal(100, 10, 12),
            'market': ['Market1'] * 12
        }, index=dates)

    def test_initialization_empty(self, empty_comparison):
        """Test initialization with no models."""
        assert len(empty_comparison.models) == 0
        assert isinstance(empty_comparison.models, dict)

    def test_initialization_with_models(self, comparison, mock_models):
        """Test initialization with models."""
        assert len(comparison.models) == 3
        assert set(comparison.models.keys()) == set(mock_models.keys())

    def test_add_model_success(self, empty_comparison):
        """Test adding a fitted model."""
        model = MockModel(name="TestModel", is_fitted=True)

        empty_comparison.add_model("TestModel", model)

        assert "TestModel" in empty_comparison.models
        assert empty_comparison.models["TestModel"] == model

    def test_add_model_unfitted_raises_error(self, empty_comparison):
        """Test adding unfitted model raises ValueError."""
        model = MockModel(name="UnfittedModel", is_fitted=False)

        with pytest.raises(ValueError, match="must be fitted before comparison"):
            empty_comparison.add_model("UnfittedModel", model)

    def test_compare_information_criteria(self, comparison):
        """Test information criteria comparison."""
        ic_df = comparison.compare_information_criteria()

        # Check structure
        assert isinstance(ic_df, pd.DataFrame)
        assert ic_df.index.name == 'Model'
        assert 'AIC' in ic_df.columns
        assert 'BIC' in ic_df.columns

        # Check values
        assert len(ic_df) == 3
        assert ic_df.loc['Model1', 'AIC'] == 100
        assert ic_df.loc['Model2', 'AIC'] == 95
        assert ic_df.loc['Model3', 'AIC'] == 105

        # Check best model identification
        assert ic_df.loc['Model2', 'AIC_best'] == '✓'
        assert ic_df.loc['Model2', 'BIC_best'] == '✓'

    def test_compare_information_criteria_empty_models(self, empty_comparison):
        """Test IC comparison with no models."""
        ic_df = empty_comparison.compare_information_criteria()

        assert isinstance(ic_df, pd.DataFrame)
        assert len(ic_df) == 0

    def test_compare_coefficients(self, comparison):
        """Test coefficient comparison."""
        coef_comparison = comparison.compare_coefficients('alpha')

        # Check structure
        assert isinstance(coef_comparison, dict)
        assert 'Model1' in coef_comparison
        assert 'Model2' in coef_comparison
        assert 'Model3' in coef_comparison

        # Check values
        assert coef_comparison['Model1']['alpha'] == 0.5
        assert coef_comparison['Model2']['alpha'] == 0.6
        assert coef_comparison['Model3']['alpha'] == 0.4

    def test_compare_coefficients_missing_param(self, comparison):
        """Test coefficient comparison with missing parameter."""
        coef_comparison = comparison.compare_coefficients('gamma')

        # Should return default values
        for model_name in comparison.models.keys():
            assert coef_comparison[model_name]['gamma'] == 0.5

    def test_compare_diagnostics(self, comparison):
        """Test diagnostic comparison."""
        diag_df = comparison.compare_diagnostics()

        # Check structure
        assert isinstance(diag_df, pd.DataFrame)
        assert diag_df.index.name == 'Model'
        assert 'ljung_box' in diag_df.columns
        assert 'arch_test' in diag_df.columns

        # Check values
        assert len(diag_df) == 3
        assert diag_df.loc['Model1', 'ljung_box'] == 0.05
        assert diag_df.loc['Model2', 'ljung_box'] == 0.02
        assert diag_df.loc['Model3', 'ljung_box'] == 0.08

    def test_compare_forecasts(self, comparison, sample_test_data):
        """Test forecast comparison."""
        forecast_results = comparison.compare_forecasts(steps=6, test_data=sample_test_data)

        # Check structure
        assert isinstance(forecast_results, dict)
        assert 'forecasts' in forecast_results
        assert 'metrics' in forecast_results

        # Check forecasts
        forecasts = forecast_results['forecasts']
        assert len(forecasts) == 3
        for model_name in comparison.models.keys():
            assert model_name in forecasts
            assert isinstance(forecasts[model_name], pd.DataFrame)
            assert len(forecasts[model_name]) == 6

        # Check metrics
        metrics = forecast_results['metrics']
        assert isinstance(metrics, pd.DataFrame)
        assert len(metrics) == 3
        assert 'rmse' in metrics.columns
        assert 'mae' in metrics.columns
        assert 'mape' in metrics.columns

    def test_compare_forecasts_no_test_data(self, comparison):
        """Test forecast comparison without test data."""
        forecast_results = comparison.compare_forecasts(steps=3)

        assert 'forecasts' in forecast_results
        assert 'metrics' in forecast_results
        assert forecast_results['metrics'] is None

        # Should still have forecasts
        forecasts = forecast_results['forecasts']
        assert len(forecasts) == 3

    def test_assess_dual_track_consistency(self, comparison):
        """Test dual-track consistency assessment."""
        # Mock VECM results for track models
        track1_model = comparison.models['Model1']
        track2_model = comparison.models['Model2']

        # Add mock VECM results
        track1_model.vecm_results = Mock()
        track2_model.vecm_results = Mock()
        track2_model.vecm_results.regime_assignment = np.array([0, 1, 0, 1, 0])

        consistency = comparison.assess_dual_track_consistency(
            track1=('Model1', track1_model),
            track2=('Model2', track2_model)
        )

        # Check structure
        assert isinstance(consistency, dict)
        assert 'parameter_correlation' in consistency
        assert 'regime_agreement' in consistency
        assert 'forecast_metrics' in consistency
        assert 'overall' in consistency

        # Check overall assessment
        assert consistency['overall'] in [
            "Strong agreement - use Track 2 for policy",
            "Moderate agreement - investigate differences",
            "Weak agreement - check model specifications"
        ]

    @patch('matplotlib.pyplot.show')
    @patch('matplotlib.pyplot.savefig')
    def test_plot_comparison(self, mock_savefig, mock_show, comparison):
        """Test comparison plotting."""
        with tempfile.TemporaryDirectory() as tmpdir:
            save_path = Path(tmpdir) / "comparison.png"

            comparison.plot_comparison(save_path=str(save_path))

            # Check savefig was called
            mock_savefig.assert_called_once()

    def test_plot_comparison_insufficient_models(self, empty_comparison):
        """Test plotting with insufficient models."""
        # Should handle gracefully with warning
        empty_comparison.plot_comparison()
        # No exception should be raised

    def test_plot_comparison_no_save_path(self, comparison):
        """Test plotting without saving."""
        with patch('matplotlib.pyplot.show'):
            comparison.plot_comparison()
        # Should complete without error

    def test_generate_comparison_table(self, comparison):
        """Test comparison table generation."""
        table = comparison.generate_comparison_table()

        assert isinstance(table, pd.DataFrame)
        assert len(table) == 3
        assert 'Model' in table.columns or table.index.name == 'Model'

    def test_save_results(self, comparison):
        """Test saving comparison results."""
        with tempfile.TemporaryDirectory() as tmpdir:
            output_dir = Path(tmpdir)

            comparison.save_results(output_dir)

            # Check files were created
            assert (output_dir / "information_criteria.csv").exists()
            assert (output_dir / "diagnostics.csv").exists()
            assert (output_dir / "comparison_plot.png").exists()

    def test_private_calculate_parameter_correlation(self, comparison):
        """Test parameter correlation calculation."""
        coef1 = {'alpha': 0.5, 'beta': 0.3}
        coef2 = {'alpha': 0.6, 'beta': 0.4}

        correlation = comparison._calculate_parameter_correlation(coef1, coef2)

        assert isinstance(correlation, float)
        assert -1 <= correlation <= 1

    def test_private_calculate_regime_agreement(self, comparison):
        """Test regime agreement calculation."""
        model1 = Mock()
        model1.vecm_results.regime_assignment = np.array([0, 1, 0, 1, 0])

        model2 = Mock()
        model2.vecm_results.regime_assignment = np.array([0, 1, 1, 1, 0])

        agreement = comparison._calculate_regime_agreement(model1, model2)

        assert isinstance(agreement, float)
        assert 0 <= agreement <= 1

    def test_error_handling_forecast_failure(self, comparison):
        """Test handling of forecast failures."""
        # Mock a model that raises exception during prediction
        failing_model = Mock()
        failing_model.predict.side_effect = Exception("Forecast failed")
        comparison.models['FailingModel'] = failing_model

        # Should handle gracefully
        forecast_results = comparison.compare_forecasts(steps=3)

        # Should still return results for other models
        assert 'forecasts' in forecast_results
        assert len(forecast_results['forecasts']) >= 2  # Original models should work


class TestRunModelComparison:
    """Test run_model_comparison convenience function."""

    @pytest.fixture
    def mock_models(self):
        """Create mock models for testing."""
        return {
            'Model1': MockModel(name="Model1"),
            'Model2': MockModel(name="Model2")
        }

    def test_run_model_comparison_basic(self, mock_models):
        """Test basic model comparison run."""
        comparison = run_model_comparison(mock_models)

        assert isinstance(comparison, ModelComparison)
        assert len(comparison.models) == 2

    def test_run_model_comparison_with_test_data(self, mock_models):
        """Test model comparison with test data."""
        test_data = pd.DataFrame({
            'price': np.random.normal(100, 10, 12),
            'market': ['Market1'] * 12
        })

        comparison = run_model_comparison(mock_models, test_data=test_data)

        assert isinstance(comparison, ModelComparison)

    def test_run_model_comparison_with_output_dir(self, mock_models):
        """Test model comparison with output directory."""
        with tempfile.TemporaryDirectory() as tmpdir:
            output_dir = Path(tmpdir)

            comparison = run_model_comparison(
                mock_models,
                output_dir=output_dir
            )

            assert isinstance(comparison, ModelComparison)
            # Check some output files were created
            assert any(output_dir.iterdir())


class TestComparisonSuite:
    """Test ComparisonSuite class."""

    @pytest.fixture
    def mock_models(self):
        """Create mock models for testing."""
        return {
            'Model1': MockModel(name="Model1"),
            'Model2': MockModel(name="Model2"),
            'Model3': MockModel(name="Model3")
        }

    @pytest.fixture
    def comparison_suite(self, mock_models):
        """Create ComparisonSuite instance."""
        suite = ComparisonSuite()
        suite.add_comparison("Test Comparison", mock_models)
        return suite

    def test_comparison_suite_initialization(self):
        """Test ComparisonSuite initialization."""
        suite = ComparisonSuite()
        assert len(suite.comparisons) == 0

    def test_add_comparison(self, mock_models):
        """Test adding comparison to suite."""
        suite = ComparisonSuite()
        suite.add_comparison("Test", mock_models)

        assert len(suite.comparisons) == 1
        assert isinstance(suite.comparisons[0], ModelComparison)

    def test_generate_report(self, comparison_suite):
        """Test report generation."""
        with tempfile.TemporaryDirectory() as tmpdir:
            output_path = Path(tmpdir) / "report.txt"

            report = comparison_suite.generate_report(output_path)

            assert isinstance(report, str)
            assert "Model Comparison Report" in report
            assert output_path.exists()

    def test_generate_report_no_output_path(self, comparison_suite):
        """Test report generation without output path."""
        report = comparison_suite.generate_report()

        assert isinstance(report, str)
        assert "Model Comparison Report" in report


class TestEdgeCasesAndErrorHandling:
    """Test edge cases and error handling."""

    def test_empty_models_dict(self):
        """Test behavior with empty models dictionary."""
        comparison = ModelComparison({})

        # All methods should handle empty models gracefully
        ic_df = comparison.compare_information_criteria()
        assert len(ic_df) == 0

        diag_df = comparison.compare_diagnostics()
        assert len(diag_df) == 0

        coef_comp = comparison.compare_coefficients()
        assert len(coef_comp) == 0

    def test_single_model_comparison(self):
        """Test comparison with single model."""
        model = MockModel(name="SingleModel")
        comparison = ModelComparison({'SingleModel': model})

        # Should work with single model
        ic_df = comparison.compare_information_criteria()
        assert len(ic_df) == 1

        # Plotting should warn about insufficient models
        comparison.plot_comparison()

    def test_models_with_missing_methods(self):
        """Test handling of models missing expected methods."""
        incomplete_model = Mock()
        incomplete_model.is_fitted = True
        incomplete_model.get_information_criteria.side_effect = AttributeError("Method not found")

        comparison = ModelComparison({'IncompleteModel': incomplete_model})

        # Should handle missing methods gracefully
        ic_df = comparison.compare_information_criteria()
        # Should return empty or handle error appropriately

    def test_models_with_different_ic_keys(self):
        """Test models returning different IC keys."""
        model1 = MockModel(name="Model1", ic={'AIC': 100, 'BIC': 110})
        model2 = MockModel(name="Model2", ic={'AIC': 95, 'HQIC': 105})  # Different keys

        comparison = ModelComparison({'Model1': model1, 'Model2': model2})

        ic_df = comparison.compare_information_criteria()

        # Should handle different keys gracefully
        assert len(ic_df) == 2
        assert 'AIC' in ic_df.columns

    def test_forecast_with_different_prediction_formats(self):
        """Test forecasting with models returning different formats."""
        model1 = MockModel(name="Model1")

        # Model that returns different format
        model2 = Mock()
        model2.is_fitted = True
        model2.predict.return_value = pd.Series([1, 2, 3])  # Series instead of DataFrame

        comparison = ModelComparison({'Model1': model1, 'Model2': model2})

        # Should handle different return formats
        forecast_results = comparison.compare_forecasts(steps=3)
        assert 'forecasts' in forecast_results

    def test_coefficient_comparison_with_complex_structures(self):
        """Test coefficient comparison with complex coefficient structures."""
        # Model with nested coefficient structure
        complex_model = MockModel(
            name="ComplexModel",
            coefficients={'alpha': {'value': 0.5, 'std_err': 0.1}}
        )

        simple_model = MockModel(
            name="SimpleModel",
            coefficients={'alpha': 0.6}
        )

        comparison = ModelComparison({
            'ComplexModel': complex_model,
            'SimpleModel': simple_model
        })

        # Should handle different coefficient structures
        coef_comp = comparison.compare_coefficients('alpha')
        assert 'ComplexModel' in coef_comp
        assert 'SimpleModel' in coef_comp

    def test_large_number_of_models(self):
        """Test performance with large number of models."""
        models = {}
        for i in range(20):
            models[f'Model{i}'] = MockModel(
                name=f"Model{i}",
                ic={'AIC': 100 + i, 'BIC': 110 + i}
            )

        comparison = ModelComparison(models)

        # Should handle large number of models
        ic_df = comparison.compare_information_criteria()
        assert len(ic_df) == 20

        # Best model identification should work
        assert ic_df['AIC_best'].sum() == 1  # Only one best model

    def test_unicode_and_special_characters_in_names(self):
        """Test handling of unicode and special characters in model names."""
        models = {
            'Model_α': MockModel(name="Model_α"),
            'Model-β': MockModel(name="Model-β"),
            'Model (γ)': MockModel(name="Model (γ)")
        }

        comparison = ModelComparison(models)

        # Should handle special characters in names
        ic_df = comparison.compare_information_criteria()
        assert len(ic_df) == 3
        assert 'Model_α' in ic_df.index

    def test_memory_efficiency_with_large_forecasts(self):
        """Test memory efficiency with large forecast arrays."""
        # Model that returns large forecasts
        large_model = MockModel(name="LargeModel")
        large_model.predict = lambda steps=1, **kwargs: pd.DataFrame({
            'prediction': np.random.normal(100, 10, steps)
        })

        comparison = ModelComparison({'LargeModel': large_model})

        # Should handle large forecasts efficiently
        forecast_results = comparison.compare_forecasts(steps=1000)
        assert len(forecast_results['forecasts']['LargeModel']) == 1000

    def test_thread_safety_simulation(self):
        """Test thread safety by simulating concurrent access."""
        import threading
        import time

        comparison = ModelComparison()
        results = []

        def add_models_concurrently(thread_id):
            for i in range(5):
                model = MockModel(name=f"Thread{thread_id}_Model{i}")
                comparison.add_model(f"Thread{thread_id}_Model{i}", model)
                time.sleep(0.001)  # Small delay to increase chance of race conditions

        # Create multiple threads
        threads = []
        for i in range(3):
            thread = threading.Thread(target=add_models_concurrently, args=(i,))
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Should have all models added
        assert len(comparison.models) == 15

# Simple Threshold VECM

## Overview

The `SimpleThresholdVECM` class implements a pragmatic threshold Vector Error Correction Model with a single threshold based on conflict intensity. This model provides clear identification of regime-dependent market integration, making it ideal for policy analysis and interpretation.

## Key Features

- **Single threshold estimation** using <PERSON> (1999) methodology
- **Regime-specific adjustment speeds** for low/high conflict periods
- **Bootstrap inference** for threshold significance testing
- **Transition probability matrix** for regime dynamics
- **Clear economic interpretation** for policy recommendations

## Class: `SimpleThresholdVECM`

### Inheritance

```python
class SimpleThresholdVECM(VECMBase, ThresholdModelMixin)
```

### Initialization

```python
SimpleThresholdVECM(
    threshold: float = 50.0,
    threshold_variable: str = 'conflict_intensity',
    n_coint: int = 1,
    n_lags: int = 2,
    trim_pct: float = 0.15,
    n_boot: int = 1000,
    name: str = None
)
```

**Parameters:**

- `threshold`: Initial threshold value (can be estimated)
- `threshold_variable`: Variable to use for threshold
- `n_coint`: Number of cointegrating relationships
- `n_lags`: Number of lags in the model
- `trim_pct`: Trimming percentage for threshold search
- `n_boot`: Number of bootstrap replications for testing
- `name`: Optional model name

### Methods

#### `fit(data: pd.DataFrame, estimate_threshold: bool = True, price_col: str = 'price_usd', **kwargs) -> SimpleThresholdVECM`

Fit the threshold VECM model.

**Parameters:**

- `data`: Panel data with prices and threshold variable
- `estimate_threshold`: Whether to estimate optimal threshold
- `price_col`: Name of price column
- `**kwargs`: Additional arguments for prepare_data

**Returns:**

- Self for method chaining

**Example:**

```python
model = SimpleThresholdVECM(threshold_variable='conflict_intensity')
model.fit(panel_data, estimate_threshold=True)
```

#### `predict(steps: int = 1, exog: Optional[pd.DataFrame] = None, regime: Optional[str] = None) -> pd.DataFrame`

Generate predictions from the fitted model.

**Parameters:**

- `steps`: Number of steps ahead to forecast
- `exog`: Optional exogenous variables
- `regime`: Force specific regime ('low', 'high', or None for auto)

**Returns:**

- DataFrame of predictions

#### `analyze_regime_dynamics() -> Dict[str, Any]`

Analyze differences between regimes.

**Returns:**

- Dictionary with:
  - `n_obs_low`: Number of observations in low regime
  - `n_obs_high`: Number of observations in high regime
  - `threshold`: Estimated threshold value
  - `alpha_difference`: Difference in adjustment speeds
  - `mean_alpha_diff`: Mean absolute difference
  - `alpha_diff_significant`: Whether speeds differ significantly
  - `prob_stay_low`: Probability of staying in low regime
  - `prob_stay_high`: Probability of staying in high regime
  - `expected_low_duration`: Expected duration in low regime
  - `expected_high_duration`: Expected duration in high regime

#### `plot_threshold_dynamics(save_path: Optional[str] = None) -> None`

Plot threshold variable and regime assignment over time.

**Parameters:**

- `save_path`: Optional path to save figure

### Results Class: `ThresholdVECMResults`

Extended results container for threshold models.

**Additional Attributes:**

- `threshold_test_stat`: LM test statistic for threshold effect
- `threshold_p_value`: Bootstrap p-value
- `threshold_ci_lower`: Lower confidence bound
- `threshold_ci_upper`: Upper confidence bound
- `low_regime_alpha`: Adjustment speeds in low conflict regime
- `high_regime_alpha`: Adjustment speeds in high conflict regime
- `regime_assignment`: Binary array of regime assignments
- `n_obs_low`: Observations in low regime
- `n_obs_high`: Observations in high regime
- `transition_probs`: 2x2 transition probability matrix

## Threshold Estimation

### Grid Search Algorithm

The optimal threshold is found by minimizing the sum of squared residuals:

```python
threshold* = argmin_{τ ∈ Γ} SSR(τ)
```

Where:

- Γ is the set of threshold values after trimming
- SSR(τ) is the sum of squared residuals for threshold τ

### Hansen (1999) Test

The null hypothesis of no threshold effect is tested using:

```text
LM = n * (SSR_linear - SSR_threshold) / SSR_threshold
```

P-values are computed via bootstrap under the null.

### Confidence Intervals

Threshold confidence intervals are constructed using the likelihood ratio:

```text
LR(τ) = n * (SSR(τ) - SSR(τ*)) / SSR(τ*)
CI = {τ : LR(τ) < χ²₁(0.95)}
```

## Regime Dynamics

### Transition Probabilities

The model estimates a 2x2 transition matrix:

```text
P = | p₀₀  p₀₁ |
    | p₁₀  p₁₁ |
```

Where p_{ij} is the probability of transitioning from regime i to regime j.

### Expected Duration

Expected regime durations are calculated as:

- Low regime: 1/(1-p₀₀)
- High regime: 1/(1-p₁₁)

## Usage Example

### Complete Workflow

```python
import pandas as pd
from yemen_market.models.track2_simple.threshold_vecm import SimpleThresholdVECM

# Load data
panel_data = pd.read_parquet('data/processed/smart_panel_monthly.parquet')
wheat_data = panel_data[panel_data['commodity'] == 'Wheat'].copy()

# Initialize model
model = SimpleThresholdVECM(
    threshold=50.0,  # Initial guess from EDA
    threshold_variable='conflict_intensity',
    n_coint=1,
    n_lags=2,
    trim_pct=0.15,
    n_boot=1000
)

# Fit model with threshold estimation
model.fit(wheat_data, estimate_threshold=True)

# View results
results = model.vecm_results
print(f"Estimated threshold: {results.threshold_value:.2f}")
print(f"95% CI: [{results.threshold_ci_lower:.2f}, {results.threshold_ci_upper:.2f}]")
print(f"Threshold test p-value: {results.threshold_p_value:.4f}")

# Analyze regime dynamics
regime_info = model.analyze_regime_dynamics()
print(f"\nRegime distribution:")
print(f"Low conflict: {regime_info['n_obs_low']} obs ({regime_info['n_obs_low']/(regime_info['n_obs_low']+regime_info['n_obs_high'])*100:.1f}%)")
print(f"High conflict: {regime_info['n_obs_high']} obs")

print(f"\nRegime persistence:")
print(f"Expected low duration: {regime_info['expected_low_duration']:.1f} months")
print(f"Expected high duration: {regime_info['expected_high_duration']:.1f} months")

# Generate regime-specific predictions
predictions_low = model.predict(steps=6, regime='low')
predictions_high = model.predict(steps=6, regime='high')

# Plot regime dynamics
model.plot_threshold_dynamics(save_path='threshold_dynamics.png')

# Run diagnostics
diagnostics = model.run_diagnostics()
```

### Interpreting Results

1. **Threshold Value**: The conflict intensity level that triggers regime change
2. **Adjustment Speeds**: How quickly prices converge to equilibrium in each regime
3. **Regime Persistence**: How stable each regime is over time
4. **Significance Testing**: Whether the threshold effect is statistically significant

## Economic Interpretation

### Low Conflict Regime (τ ≤ threshold)

- Markets function relatively normally
- Price signals transmitted efficiently
- Adjustment to shocks is rapid

### High Conflict Regime (τ > threshold)

- Market fragmentation increases
- Price transmission weakened
- Adjustment speeds may slow or accelerate depending on market responses

### Policy Implications

- Threshold identifies critical conflict levels for market intervention
- Regime-specific parameters inform targeted policies
- Transition probabilities help predict market dynamics

## Computational Details

- **Estimation Time**: 1-5 minutes depending on bootstrap replications
- **Memory Usage**: Minimal (< 100MB for typical datasets)
- **Parallelization**: Bootstrap can be parallelized
- **Numerical Stability**: Uses QR decomposition for regression

## References

1. Hansen, B. E. (1999). Threshold effects in non-dynamic panels: Estimation, testing, and inference. *Journal of Econometrics*, 93(2), 345-368.

2. Hansen, B. E., & Seo, B. (2002). Testing for two-regime threshold cointegration in vector error-correction models. *Journal of Econometrics*, 110(2), 293-318.

3. Balke, N. S., & Fomby, T. B. (1997). Threshold cointegration. *International Economic Review*, 38(3), 627-645.

## See Also

- [Base Model Classes](../base.md)
- [Track 1: Bayesian TVP-VECM](../track1_complex/tvp_vecm.md)
- [Threshold Model Mixin](../base.md#thresholdmodelmixin)
- [Model Comparison Framework](../model_comparison.md)

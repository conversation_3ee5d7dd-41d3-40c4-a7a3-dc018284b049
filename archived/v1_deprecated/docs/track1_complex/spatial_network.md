# Spatial Network Components

## Overview

The `spatial_network.py` module implements spatial weight matrices and network-augmented models that account for geographic spillovers and market connectivity in Yemen. It supports multiple weight specifications and provides tools for visualizing market networks.

## Classes

### `SpatialWeightMatrix`

Create and manage spatial weight matrices for market integration analysis.

#### Initialization

```python
SpatialWeightMatrix(
    markets_df: pd.DataFrame,
    weight_type: str = 'distance',
    normalize: bool = True
)
```

**Parameters:**

- `markets_df`: DataFrame with market information (must have lat/lon or geometry)
- `weight_type`: Type of weights ('distance', 'contiguity', 'network', 'hybrid')
- `normalize`: Whether to row-normalize the weight matrix

#### Methods

##### `create_weight_matrix(**kwargs) -> np.ndarray`

Create spatial weight matrix based on specified type.

**Parameters vary by weight type:**

**Distance weights:**

- `cutoff_km` (float): Maximum distance for non-zero weights (default: 200)
- `decay` (str): Decay function ('inverse', 'exponential', 'gaussian')
- `alpha` (float): Decay parameter (default: 1.0)

**Contiguity weights:**

- `zone_col` (str): Column containing zone assignments (default: 'control_zone')
- `boundary_bonus` (float): Extra weight for boundary markets (default: 2.0)

**Network weights:**

- `trade_routes` (List[Tuple[str, str]]): List of (market1, market2) trade connections
- `conflict_corridors` (pd.DataFrame): DataFrame with conflict intensity between markets

**Hybrid weights:**

- `distance_weight` (float): Weight for distance component (default: 0.5)
- `contiguity_weight` (float): Weight for contiguity component (default: 0.3)
- `network_weight` (float): Weight for network component (default: 0.2)

**Returns:**

- Spatial weight matrix (n x n numpy array)

##### `get_neighbors(market: str, threshold: float = 0.1) -> List[Tuple[str, float]]`

Get neighbors of a market with weights above threshold.

**Parameters:**

- `market`: Market name
- `threshold`: Minimum weight threshold

**Returns:**

- List of (neighbor_name, weight) tuples sorted by weight

##### `calculate_spatial_lag(values: pd.Series) -> pd.Series`

Calculate spatial lag of a variable.

**Parameters:**

- `values`: Series with values for each market

**Returns:**

- Series with spatial lag values (W * y)

##### `plot_network(save_path: Optional[str] = None, edge_threshold: float = 0.1) -> None`

Plot spatial network visualization.

**Parameters:**

- `save_path`: Optional path to save figure
- `edge_threshold`: Minimum weight to show edge

### `SpatialVECM`

Spatial Vector Error Correction Model that extends standard VECM with spatial lags.

#### Model Initialization

```python
SpatialVECM(base_model, spatial_weights: np.ndarray)
```

**Parameters:**

- `base_model`: Base VECM model to extend
- `spatial_weights`: Spatial weight matrix

#### VECM Methods

##### `add_spatial_lags(data: pd.DataFrame, lag_variables: List[str]) -> pd.DataFrame`

Add spatial lags of specified variables to the dataset.

**Parameters:**

- `data`: Panel data
- `lag_variables`: Variables to create spatial lags for

**Returns:**

- Data with spatial lag columns added (named as `spatial_lag_{variable}`)

##### `test_spatial_autocorrelation(residuals: pd.Series) -> Dict[str, float]`

Test for spatial autocorrelation in residuals using Moran's I.

**Parameters:**

- `residuals`: Model residuals

**Returns:**

- Dictionary with:
  - `morans_i`: Moran's I statistic
  - `p_value`: P-value for significance test
  - `interpretation`: Text interpretation of results

## Weight Matrix Types

### Distance-Based Weights

Creates weights based on geographic distance between markets:

```python
w_ij = f(d_ij) if d_ij ≤ cutoff_km, else 0
```

Where f() is the decay function:

- **Inverse**: `w_ij = 1 / d_ij^α`
- **Exponential**: `w_ij = exp(-α * d_ij / cutoff_km)`
- **Gaussian**: `w_ij = exp(-0.5 * (d_ij / (cutoff_km/3))²)`

### Contiguity-Based Weights

Creates weights based on shared control zones:

- Markets in same zone: w_ij = 1
- Boundary markets: w_ij *= boundary_bonus
- Different zones: w_ij = 0

### Network-Based Weights

Combines distance with trade routes and conflict corridors:

- Base: exponential distance decay
- Enhanced for known trade routes
- Reduced for high-conflict corridors

### Hybrid Weights

Weighted combination of all three types:

```python
W = α * W_distance + β * W_contiguity + γ * W_network
```

## Usage Examples

### Creating Basic Distance Weights

```python
import pandas as pd
from yemen_market.models.track1_complex.spatial_network import SpatialWeightMatrix

# Load market data with coordinates
markets = pd.DataFrame({
    'market_name': ['Sana\'a', 'Aden', 'Taiz', 'Al Hudaydah'],
    'latitude': [15.3694, 12.7855, 13.5795, 14.7981],
    'longitude': [44.1910, 45.0187, 44.0178, 42.9545]
})

# Create distance-based weights
sw = SpatialWeightMatrix(markets, weight_type='distance', normalize=True)
W = sw.create_weight_matrix(cutoff_km=200, decay='exponential', alpha=1.0)

# Get neighbors of Sana'a
neighbors = sw.get_neighbors('Sana\'a', threshold=0.1)
for market, weight in neighbors:
    print(f"{market}: {weight:.3f}")
```

### Creating Contiguity Weights with Zones

```python
# Add control zone information
markets['control_zone'] = ['DFA', 'IRG', 'DFA', 'DFA']
markets['is_boundary'] = [False, False, True, False]

# Create contiguity weights
sw = SpatialWeightMatrix(markets, weight_type='contiguity')
W = sw.create_weight_matrix(zone_col='control_zone', boundary_bonus=2.0)
```

### Calculating Spatial Lags

```python
# Price data for markets
prices = pd.Series([100, 95, 98, 102], index=markets['market_name'])

# Calculate spatial lag
sw = SpatialWeightMatrix(markets, weight_type='distance')
W = sw.create_weight_matrix()
spatial_lag_prices = sw.calculate_spatial_lag(prices)

print("Original prices:", prices.values)
print("Spatial lag prices:", spatial_lag_prices.values)
```

### Using with VECM Models

```python
from yemen_market.models.track1_complex.spatial_network import SpatialVECM

# Assume we have a fitted VECM model and panel data
spatial_vecm = SpatialVECM(base_model=vecm_model, spatial_weights=W)

# Add spatial lags to data
data_with_lags = spatial_vecm.add_spatial_lags(
    panel_data, 
    lag_variables=['price_usd', 'conflict_intensity']
)

# Test for spatial autocorrelation in residuals
residuals = vecm_model.get_residuals()
spatial_test = spatial_vecm.test_spatial_autocorrelation(residuals)
print(f"Moran's I: {spatial_test['morans_i']:.4f}, p-value: {spatial_test['p_value']:.4f}")
```

### Visualizing the Network

```python
# Create and plot network
sw = SpatialWeightMatrix(markets, weight_type='distance')
W = sw.create_weight_matrix(cutoff_km=300)

# Plot network with edge threshold
sw.plot_network(save_path='market_network.png', edge_threshold=0.1)
```

## Integration with Models

The spatial components can be integrated into both Track 1 and Track 2 models:

### Track 1 (Bayesian TVP-VECM)

- Spatial weights can modulate parameter evolution
- Network structure informs hierarchical priors
- Spatial spillovers captured in volatility

### Track 2 (Threshold VECM)

- Spatial lags as additional regressors
- Threshold may vary by spatial clusters
- Regime spillovers across neighbors

## Performance Considerations

- **Matrix Sparsity**: Use `to_sparse()` for large networks
- **Normalization**: Row-normalization ensures weights sum to 1
- **Isolated Markets**: Check for markets with no connections
- **Computation**: Distance calculations can be vectorized

## References

1. LeSage, J., & Pace, R. K. (2009). *Introduction to Spatial Econometrics*. CRC Press.

2. Anselin, L. (1988). *Spatial Econometrics: Methods and Models*. Kluwer Academic Publishers.

3. Elhorst, J. P. (2014). *Spatial Econometrics: From Cross-Sectional Data to Spatial Panels*. Springer.

## See Also

- [Base Model Classes](../base.md)
- [Bayesian TVP-VECM](tvp_vecm.md)
- [Spatial Model Mixin](../base.md#spatialmodelmixin)
- [Visualization Guide](../../../guides/visualization_guide.md)

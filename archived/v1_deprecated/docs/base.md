# Base Model Classes API Reference

## Overview

The `base.py` module provides abstract base classes and common functionality for all econometric models in the Yemen market integration analysis. It includes the core infrastructure for VECM models, diagnostic testing, and model comparison.

## Classes

### `ModelType`

Enumeration of supported model types.

```python
class ModelType(Enum):
    VECM = "vecm"
    THRESHOLD_VECM = "threshold_vecm"
    TVP_VECM = "tvp_vecm"
    SPATIAL_VECM = "spatial_vecm"
    BAYESIAN_VECM = "bayesian_vecm"
```

### `VECMResults`

Container for VECM-specific estimation results.

**Attributes:**

- `alpha` (np.ndarray): Adjustment speed parameters
- `beta` (np.ndarray): Cointegrating vectors
- `gamma` (Optional[np.ndarray]): Short-run dynamics coefficients
- `threshold_value` (Optional[float]): Estimated threshold (if applicable)
- `threshold_variable` (Optional[str]): Variable used for threshold
- `regime_coefficients` (Optional[Dict]): Regime-specific parameters
- `time_varying_params` (Optional[Dict]): Time-varying parameters
- `spatial_coefficients` (Optional[np.ndarray]): Spatial lag coefficients
- `log_likelihood` (float): Log-likelihood value
- `aic` (float): Akaike Information Criterion
- `bic` (float): Bayesian Information Criterion
- `n_obs` (int): Number of observations
- `n_coint` (int): Number of cointegrating relationships
- `converged` (bool): Whether estimation converged

### `BaseEconometricModel`

Abstract base class for all econometric models.

**Methods:**

#### `fit(data: pd.DataFrame, **kwargs) -> BaseEconometricModel`

Fit the model to data. Must be implemented by subclasses.

**Parameters:**

- `data`: Panel data for estimation
- `**kwargs`: Model-specific parameters

**Returns:**

- Self for method chaining

#### `predict(steps: int = 1, exog: Optional[pd.DataFrame] = None) -> pd.DataFrame`

Generate predictions from the fitted model.

**Parameters:**

- `steps`: Number of steps ahead to forecast
- `exog`: Optional exogenous variables for prediction

**Returns:**

- DataFrame of predictions

#### `run_diagnostics(tests: Optional[List[str]] = None) -> Dict[str, Any]`

Run diagnostic tests on the fitted model.

**Parameters:**

- `tests`: Optional list of specific tests to run

**Returns:**

- Dictionary of test results

#### `get_information_criteria() -> Dict[str, float]`

Get information criteria for model comparison.

**Returns:**

- Dictionary with AIC, BIC, HQIC, and log-likelihood

### `VECMBase`

Base class for Vector Error Correction Models, extending `BaseEconometricModel`.

**Methods:**

#### `__init__(n_coint: int = 1, n_lags: int = 2, deterministic: str = 'ci', name: str = None)`

Initialize VECM base model.

**Parameters:**

- `n_coint`: Number of cointegrating relationships
- `n_lags`: Number of lags in the model
- `deterministic`: Deterministic trend specification
  - 'n': no deterministic terms
  - 'c': constant term only
  - 'ct': constant and trend
  - 'ci': constant in cointegrating relation (default)
  - 'cit': constant and trend in cointegrating relation
- `name`: Optional model name

#### `prepare_data(data: pd.DataFrame, price_cols: List[str], exog_cols: Optional[List[str]] = None) -> Dict[str, Any]`

Prepare data for VECM estimation.

**Parameters:**

- `data`: Panel data with date, market, and price columns
- `price_cols`: List of price column names to include
- `exog_cols`: Optional exogenous variable columns

**Returns:**

- Dictionary with prepared matrices and metadata:
  - `log_prices`: Log-transformed price matrix
  - `dlogs`: First differences of log prices
  - `n_obs`: Number of observations
  - `n_vars`: Number of variables
  - `markets`: List of market names
  - `dates`: DatetimeIndex of dates
  - `exog`: Optional exogenous variables

#### `test_cointegration(data: Dict[str, Any]) -> Dict[str, Any]`

Test for cointegration using Johansen test.

**Parameters:**

- `data`: Prepared data dictionary from prepare_data

**Returns:**

- Dictionary with test results:
  - `trace_stats`: Trace statistics
  - `eigen_stats`: Eigenvalue statistics
  - `selected_rank`: Recommended cointegration rank
  - `eigenvalues`: Eigenvalues from test
  - `eigenvectors`: Eigenvectors (cointegrating vectors)

#### `calculate_error_correction_terms(log_prices: np.ndarray, beta: np.ndarray) -> np.ndarray`

Calculate error correction terms.

**Parameters:**

- `log_prices`: Log price matrix (T x N)
- `beta`: Cointegrating vectors (N x r)

**Returns:**

- Error correction terms (T x r)

#### `estimate_adjustment_speeds(dlogs: np.ndarray, ect: np.ndarray, lags: int) -> Dict[str, np.ndarray]`

Estimate adjustment speed parameters.

**Parameters:**

- `dlogs`: First differences of log prices
- `ect`: Error correction terms
- `lags`: Number of lags

**Returns:**

- Dictionary with:
  - `alpha`: Adjustment speed coefficients
  - `alpha_se`: Standard errors
  - `gamma`: Short-run dynamics (if lags > 1)

#### `test_weak_exogeneity() -> Dict[str, Any]`

Test weak exogeneity of variables.

**Returns:**

- Dictionary with test results for each variable:
  - `alpha`: Adjustment speed coefficient
  - `t_stat`: T-statistic
  - `p_value`: P-value
  - `is_weakly_exogenous`: Boolean indicator

### Mixins

#### `ThresholdModelMixin`

Mixin for models with threshold effects.

**Methods:**

- `estimate_threshold(threshold_var: str, trim: float = 0.15, n_boot: int = 1000) -> Dict[str, Any]`: Estimate threshold value and test significance
- `plot_threshold_search(save_path: Optional[Path] = None) -> None`: Plot likelihood ratio statistics for threshold search

#### `SpatialModelMixin`

Mixin for models with spatial components.

**Methods:**

- `calculate_spatial_weights(weight_type: str = 'distance', k_neighbors: int = 5) -> np.ndarray`: Calculate spatial weight matrix
- `test_spatial_autocorrelation() -> Dict[str, Any]`: Test for spatial autocorrelation in residuals

#### `PanelModelMixin`

Mixin for panel data models.

**Methods:**

- `test_poolability() -> Dict[str, Any]`: Test whether panel can be pooled
- `hausman_test(alternative_model: BaseEconometricModel) -> Dict[str, Any]`: Hausman test for model specification

## Usage Examples

### Basic VECM Implementation

```python
from yemen_market.models.base import VECMBase, VECMResults

class SimpleVECM(VECMBase):
    def fit(self, data: pd.DataFrame, **kwargs):
        # Prepare data
        prepared_data = self.prepare_data(data, price_cols=['price_usd'])
        
        # Test for cointegration
        coint_results = self.test_cointegration(prepared_data)
        self.n_coint = coint_results['selected_rank']
        
        # Extract cointegrating vectors
        beta = coint_results['eigenvectors'][:, :self.n_coint]
        
        # Calculate error correction terms
        log_prices = prepared_data['log_prices'].values
        ect = self.calculate_error_correction_terms(log_prices[:-1], beta)
        
        # Estimate adjustment speeds
        dlogs = prepared_data['dlogs'].values
        adj_results = self.estimate_adjustment_speeds(dlogs, ect, self.n_lags)
        
        # Create results object
        self.vecm_results = VECMResults(
            alpha=adj_results['alpha'],
            beta=beta,
            n_obs=prepared_data['n_obs'],
            n_coint=self.n_coint,
            converged=True
        )
        
        self.is_fitted = True
        return self
```

### Using Diagnostic Tests

```python
# After fitting a model
model = SimpleVECM()
model.fit(data)

# Run all diagnostics
diagnostics = model.run_diagnostics()

# Run specific tests
diagnostics = model.run_diagnostics(tests=['serial_correlation', 'normality'])

# Test weak exogeneity
exogeneity_results = model.test_weak_exogeneity()
for market, result in exogeneity_results.items():
    if result['is_weakly_exogenous']:
        print(f"{market} is weakly exogenous (p={result['p_value']:.4f})")
```

### Model Comparison

```python
# Get information criteria
ic = model.get_information_criteria()
print(f"AIC: {ic['AIC']:.2f}, BIC: {ic['BIC']:.2f}")

# Compare forecasts
test_data = panel_data[panel_data['date'] >= '2024-01-01']
forecast_metrics = model.forecast_evaluation(test_data)
print(f"RMSE: {forecast_metrics['rmse']:.4f}")
```

## See Also

- [Track 1: Bayesian TVP-VECM](track1_complex/tvp_vecm.md)
- [Track 2: Simple Threshold VECM](track2_simple/threshold_vecm.md)
- [Diagnostic Framework](../diagnostics/test_battery.md)
- [Model Comparison](model_comparison.md)

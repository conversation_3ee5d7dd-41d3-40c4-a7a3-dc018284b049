#!/usr/bin/env python3
"""Test script to verify diagnostic framework integration with three-tier models."""

import sys
import pandas as pd
import numpy as np
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from yemen_market.utils.logging import setup_logging, info, warning, error
from yemen_market.models.three_tier.integration.three_tier_runner import ThreeTierAnalysis
from yemen_market.models.three_tier.diagnostics import ThreeTierPanelDiagnostics


def create_test_data():
    """Create synthetic test data for diagnostic testing."""
    np.random.seed(42)
    
    # Create panel structure
    markets = ['Sana\'a', 'Aden', 'Taiz', 'Hodeidah', 'Ibb']
    commodities = ['wheat', 'rice', 'sugar']
    dates = pd.date_range('2020-01-01', '2023-12-31', freq='W')
    
    # Create all combinations
    data = []
    for market in markets:
        for commodity in commodities:
            for date in dates:
                # Base price with market and commodity effects
                base_price = 100
                market_effect = np.random.normal(0, 10) if market in ['<PERSON><PERSON>\'a', 'Aden'] else np.random.normal(20, 15)
                commodity_effect = {'wheat': 0, 'rice': 10, 'sugar': -5}[commodity]
                
                # Add time trend and seasonality
                time_trend = 0.1 * (date - dates[0]).days / 365
                seasonal = 5 * np.sin(2 * np.pi * date.dayofyear / 365)
                
                # Add some serial correlation and heteroskedasticity
                ar_component = 0.5 * np.random.normal(0, 1) if len(data) > 0 else 0
                error = np.random.normal(0, 5 * (1 + 0.1 * time_trend))  # Heteroskedastic errors
                
                price = base_price + market_effect + commodity_effect + time_trend + seasonal + ar_component + error
                
                data.append({
                    'market': market,
                    'commodity': commodity,
                    'date': date,
                    'price': max(price, 10),  # Ensure positive prices
                    'conflict_intensity': np.random.poisson(2) if market in ['Taiz', 'Hodeidah'] else np.random.poisson(0.5)
                })
    
    df = pd.DataFrame(data)
    
    # Add some missing values to test missing data diagnostics
    missing_mask = np.random.random(len(df)) < 0.05
    df.loc[missing_mask, 'price'] = np.nan
    
    return df


def test_diagnostic_integration():
    """Test the diagnostic framework integration."""
    setup_logging()
    
    info("="*60)
    info("Testing Diagnostic Framework Integration")
    info("="*60)
    
    # Create test data
    info("\n1. Creating synthetic test data...")
    data = create_test_data()
    info(f"Created panel data: {len(data)} observations")
    info(f"Markets: {data['market'].nunique()}")
    info(f"Commodities: {data['commodity'].nunique()}")
    info(f"Time periods: {data['date'].nunique()}")
    
    # Configure three-tier analysis with diagnostics enabled
    config = {
        'tier1_config': {
            'model_type': 'pooled',
            'include_time_effects': True,
            'include_entity_effects': True,
            'standard_errors': 'cluster'
        },
        'tier2_config': {
            'threshold_variable': 'conflict_intensity',
            'min_obs_per_regime': 30
        },
        'tier3_config': {
            'n_factors': 2
        },
        'diagnostic_config': {
            'run_wooldridge_test': True,
            'run_pesaran_cd_test': True,
            'run_modified_wald_test': True,
            'run_breusch_pagan_test': True,
            'check_unit_roots': False  # Skip for speed
        },
        'run_diagnostics': True,  # Enable diagnostics
        'output_dir': 'results/diagnostic_test'
    }
    
    # Run three-tier analysis
    info("\n2. Running three-tier analysis with diagnostics...")
    analysis = ThreeTierAnalysis(config)
    
    try:
        results = analysis.run_full_analysis(data)
        
        # Check Tier 1 diagnostics
        info("\n3. Checking Tier 1 diagnostic results...")
        tier1_results = results.get('tier1', {})
        if isinstance(tier1_results, dict) and 'diagnostics' in tier1_results:
            diag = tier1_results['diagnostics']
            info(f"Tier 1 diagnostics found: {diag is not None}")
            
            if diag:
                summary = diag.get('summary', {})
                info(f"Total tests run: {summary.get('total_tests', 0)}")
                info(f"Tests passed: {summary.get('passed_tests', 0)}")
                info(f"Tests failed: {summary.get('failed_tests', 0)}")
                
                # Show test results
                test_results = diag.get('test_results', {})
                for test_name, result in test_results.items():
                    status = "PASSED" if result.get('passed', False) else "FAILED"
                    p_value = result.get('p_value', 'N/A')
                    info(f"  - {test_name}: {status} (p-value: {p_value})")
        else:
            warning("No Tier 1 diagnostics found in results")
        
        # Check Tier 2 diagnostics
        info("\n4. Checking Tier 2 diagnostic results...")
        tier2_results = results.get('tier2', {})
        commodities_with_diagnostics = 0
        for commodity, commodity_results in tier2_results.items():
            if isinstance(commodity_results, dict) and 'diagnostics' in commodity_results:
                if commodity_results['diagnostics']:
                    commodities_with_diagnostics += 1
        
        info(f"Commodities with diagnostics: {commodities_with_diagnostics}/{len(tier2_results)}")
        
        # Check if diagnostic reports were saved
        info("\n5. Checking saved diagnostic reports...")
        output_dir = Path(config['output_dir'])
        tier1_diag_file = output_dir / 'tier1' / 'tier1_diagnostics.json'
        if tier1_diag_file.exists():
            info(f"✓ Tier 1 diagnostic report saved: {tier1_diag_file}")
        else:
            warning(f"✗ Tier 1 diagnostic report not found: {tier1_diag_file}")
        
        # Check summary report
        summary_file = output_dir / 'three_tier_analysis_report.md'
        if summary_file.exists():
            info(f"✓ Summary report saved: {summary_file}")
            
            # Check if diagnostics are mentioned in summary
            with open(summary_file, 'r') as f:
                content = f.read()
                if 'Diagnostic' in content or 'diagnostic' in content:
                    info("✓ Diagnostics mentioned in summary report")
                else:
                    warning("✗ Diagnostics not mentioned in summary report")
        
        info("\n" + "="*60)
        info("Diagnostic Integration Test Complete!")
        info("="*60)
        
        return True
        
    except Exception as e:
        error(f"Error during diagnostic integration test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_standalone_diagnostics():
    """Test diagnostics as a standalone component."""
    setup_logging()
    
    info("\n" + "="*60)
    info("Testing Standalone Diagnostic Component")
    info("="*60)
    
    # Create minimal test data
    data = create_test_data()
    
    # Create a mock ResultsContainer
    from yemen_market.models.three_tier.core.results_container import ResultsContainer
    
    # Create mock results - ResultsContainer expects commodity, model_type, and results dict
    results_dict = {
        'coefficients': {'intercept': 100.0, 'time_trend': 0.1},
        'standard_errors': {'intercept': 5.0, 'time_trend': 0.05},
        'fit_statistics': {
            'r_squared': 0.75,
            'adj_r_squared': 0.74,
            'n_obs': len(data)
        }
    }
    
    mock_results = ResultsContainer(
        commodity="test_commodity",
        model_type="test_model",
        results=results_dict
    )
    
    # Add residuals and other data via results dictionary
    mock_results.residuals = np.random.normal(0, 1, len(data))
    mock_results.fitted_values = data['price'].fillna(data['price'].mean()).values
    
    # Add panel structure info to results
    mock_results.results['panel_info'] = {
        'n_entities': data['market'].nunique(),
        'n_time_periods': data['date'].nunique(),
        'n_obs': len(data)
    }
    
    # Run diagnostics
    info("\nRunning diagnostics on mock results...")
    diagnostics = ThreeTierPanelDiagnostics(tier=1)
    report = diagnostics.run_diagnostics(mock_results, data)
    
    # Check report
    if report:
        info(f"Diagnostic report generated: {report.tier}")
        info(f"Total tests: {len(report.test_results)}")
        info(f"Critical failures: {report.has_critical_failures()}")
        
        # Show summary
        summary = report.to_dict()
        info(f"\nSummary: {summary['summary']}")
    else:
        warning("No diagnostic report generated")
    
    return True


if __name__ == "__main__":
    # Run both tests
    success1 = test_diagnostic_integration()
    success2 = test_standalone_diagnostics()
    
    if success1 and success2:
        info("\n✅ All diagnostic tests passed!")
        sys.exit(0)
    else:
        error("\n❌ Some diagnostic tests failed!")
        sys.exit(1)

# Bayesian Time-Varying Parameter VECM

## Overview

The `BayesianTVPVECM` class implements a sophisticated Bayesian Time-Varying Parameter Vector Error Correction Model where parameters can evolve over time in response to conflict intensity. This model captures the dynamic nature of market integration in conflict-affected regions.

## Key Features

- **Time-varying adjustment speeds** that respond to conflict dynamics
- **Stochastic volatility** to capture changing uncertainty
- **Hierarchical priors** for efficient pooling across markets
- **Robust to outliers** through Student-t errors
- **Conflict sensitivity analysis** to quantify impact on integration

## Class: `BayesianTVPVECM`

### Inheritance

```python
class BayesianTVPVECM(VECMBase)
```

### Initialization

```python
BayesianTVPVECM(
    n_coint: int = 1,
    n_lags: int = 2,
    n_samples: int = 2000,
    n_chains: int = 4,
    target_accept: float = 0.8,
    random_seed: int = 42,
    name: str = None
)
```

**Parameters:**

- `n_coint`: Number of cointegrating relationships
- `n_lags`: Number of lags in the model
- `n_samples`: Number of MCMC samples per chain
- `n_chains`: Number of MCMC chains
- `target_accept`: Target acceptance rate for NUTS sampler
- `random_seed`: Random seed for reproducibility
- `name`: Optional model name

### Methods

#### `build_model(data: Dict[str, Any], conflict_intensity: pd.Series) -> pm.Model`

Build the PyMC model with time-varying parameters.

**Parameters:**

- `data`: Prepared data from prepare_data method
- `conflict_intensity`: Time series of conflict intensity

**Returns:**

- PyMC model object

**Model Structure:**

```text
θ_t = θ_{t-1} + v_t, where v_t ~ N(0, Q_t)
Q_t = base_sigma * exp(conflict_sensitivity * conflict_norm)
```

#### `fit(data: pd.DataFrame, conflict_col: str = 'conflict_intensity', price_col: str = 'price_usd', **kwargs) -> BayesianTVPVECM`

Fit the Bayesian TVP-VECM model using MCMC.

**Parameters:**

- `data`: Panel data with prices and conflict intensity
- `conflict_col`: Name of conflict intensity column
- `price_col`: Name of price column
- `**kwargs`: Additional arguments for prepare_data

**Returns:**

- Self for method chaining

**Example:**

```python
model = BayesianTVPVECM(n_samples=2000, n_chains=4)
model.fit(panel_data, conflict_col='conflict_intensity')
```

#### `predict(steps: int = 1, exog: Optional[pd.DataFrame] = None, include_uncertainty: bool = True) -> Union[pd.DataFrame, Tuple[pd.DataFrame, pd.DataFrame]]`

Generate predictions from the fitted model.

**Parameters:**

- `steps`: Number of steps ahead to forecast
- `exog`: Optional exogenous variables (including future conflict)
- `include_uncertainty`: Whether to return prediction intervals

**Returns:**

- DataFrame of predictions, optionally with uncertainty bounds

#### `analyze_conflict_impact() -> Dict[str, Any]`

Analyze how conflict affects parameter evolution.

**Returns:**

- Dictionary with:
  - `conflict_sensitivity`: Posterior mean of sensitivity parameter
  - `conflict_sensitivity_std`: Standard deviation
  - `significant`: Whether effect is statistically significant
  - `ci_lower`: Lower bound of 95% credible interval
  - `ci_upper`: Upper bound of 95% credible interval

#### `plot_time_varying_parameters(market_idx: int = 0, save_path: Optional[str] = None) -> None`

Plot evolution of time-varying parameters.

**Parameters:**

- `market_idx`: Index of market to plot
- `save_path`: Optional path to save figure

### Results Class: `BayesianVECMResults`

Extended results container for Bayesian models.

**Additional Attributes:**

- `trace`: ArviZ InferenceData object with posterior samples
- `posterior_means`: Dictionary of posterior mean estimates
- `posterior_stds`: Dictionary of posterior standard deviations
- `rhat`: R-hat convergence diagnostics
- `ess`: Effective sample size
- `waic`: Watanabe-Akaike Information Criterion
- `loo`: Leave-One-Out cross-validation
- `alpha_path`: Time path of adjustment speeds (T x N x r)
- `volatility_path`: Time path of volatility (T x N)

## Model Specification

### Prior Distributions

```python
# Hyperpriors
alpha_mu_0 ~ Normal(-0.5, 0.25)
alpha_sigma_0 ~ HalfNormal(0.1)

# Initial adjustment speeds (hierarchical)
alpha_0 ~ Normal(alpha_mu_0, alpha_sigma_0)

# Evolution parameters
base_evolution_sigma ~ HalfNormal(0.01)
conflict_sensitivity ~ Normal(0, 0.1)

# Degrees of freedom for Student-t errors
nu ~ Gamma(5, 1)
```

### Time-Varying Dynamics

The adjustment speeds evolve according to:

```text
alpha_t = alpha_{t-1} + v_t
v_t ~ N(0, sigma_t^2)
sigma_t = base_sigma * exp(conflict_sensitivity * conflict_normalized)
```

This specification allows parameters to change more rapidly during high-conflict periods.

## Usage Example

### Complete Workflow

```python
import pandas as pd
from yemen_market.models.track1_complex.tvp_vecm import BayesianTVPVECM

# Load data
panel_data = pd.read_parquet('data/processed/smart_panel_monthly.parquet')

# Filter to specific commodity
wheat_data = panel_data[panel_data['commodity'] == 'Wheat'].copy()

# Initialize model
model = BayesianTVPVECM(
    n_coint=1,
    n_lags=2,
    n_samples=2000,
    n_chains=4,
    target_accept=0.8
)

# Fit model
model.fit(wheat_data, conflict_col='conflict_intensity')

# Check convergence
print(f"Converged: {model.vecm_results.converged}")
print(f"Max R-hat: {max(model.vecm_results.rhat.values()):.3f}")

# Analyze conflict impact
conflict_impact = model.analyze_conflict_impact()
print(f"Conflict sensitivity: {conflict_impact['conflict_sensitivity']:.4f}")
print(f"Significant: {conflict_impact['significant']}")

# Generate predictions with uncertainty
predictions, (lower, upper) = model.predict(steps=6, include_uncertainty=True)

# Plot time-varying parameters
model.plot_time_varying_parameters(market_idx=0, save_path='tvp_evolution.png')

# Run diagnostics
diagnostics = model.run_diagnostics()
```

### Interpreting Results

1. **Convergence**: Check R-hat < 1.01 and ESS > 400 for all parameters
2. **Conflict Impact**: Positive sensitivity means parameters vary more during conflict
3. **Time-Varying Paths**: Shows how integration changes over time
4. **Prediction Intervals**: Captures both parameter and observation uncertainty

## Computational Considerations

- **Sampling Time**: Expect 5-30 minutes depending on data size and iterations
- **Memory Usage**: Approximately 100MB per 1000 samples with 10 markets
- **Parallel Chains**: Uses multiprocessing, benefits from multiple cores
- **GPU Support**: Can use JAX backend for GPU acceleration

## Diagnostics

The model automatically computes:

- **R-hat**: Gelman-Rubin convergence diagnostic
- **ESS**: Effective sample size for each parameter
- **WAIC/LOO**: Information criteria for model comparison
- **Posterior predictive checks**: Via ArviZ

## References

1. Primiceri, G. E. (2005). Time varying structural vector autoregressions and monetary policy. *Review of Economic Studies*, 72(3), 821-852.

2. Koop, G., & Korobilis, D. (2010). Bayesian multivariate time series methods for empirical macroeconomics. *Foundations and Trends in Econometrics*, 3(4), 267-358.

## See Also

- [Base Model Classes](../base.md)
- [Track 2: Simple Threshold VECM](../track2_simple/threshold_vecm.md)
- [Spatial Network Components](spatial_network.md)
- [Model Comparison Framework](../model_comparison.md)

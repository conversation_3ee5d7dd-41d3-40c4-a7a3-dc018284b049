"""Tests for model diagnostics visualization.

This module contains comprehensive tests for the visualization functions
in the model_diagnostics module, ensuring they handle various data structures,
edge cases, and produce correct visualizations according to World Bank standards.
"""

import pytest
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from unittest.mock import Mock, patch, MagicMock
import tempfile
from pathlib import Path
import statsmodels.api as sm
from scipy import stats

from yemen_market.visualization.model_diagnostics import (
    plot_diagnostic_suite,
    plot_residual_analysis,
    plot_threshold_search,
    plot_parameter_evolution
)
from yemen_market.models.three_tier.core.base_model import BaseThreeTierModel


class TestModelDiagnostics:
    """Test suite for model diagnostic visualizations."""

    @pytest.fixture
    def setup_and_teardown(self):
        """Setup and teardown for each test."""
        # Store original plt.show and savefig to restore after test
        original_show = plt.show
        original_savefig = plt.savefig

        # Replace with mock functions
        plt.show = MagicMock()
        plt.savefig = MagicMock()

        yield

        # Restore original functions
        plt.show = original_show
        plt.savefig = original_savefig
        plt.close('all')  # Close all figures

    @pytest.fixture
    def mock_model(self):
        """Create a mock econometric model for testing."""
        model = Mock(spec=BaseThreeTierModel)
        model.name = "TestModel"
        model.get_residuals.return_value = pd.Series(
            np.random.normal(0, 1, 100),
            index=pd.date_range('2020-01-01', periods=100, freq='D')
        )
        model.get_results.return_value = Mock()
        return model

    @pytest.fixture
    def mock_panel_model(self):
        """Create a mock model with panel data residuals."""
        model = Mock(spec=BaseThreeTierModel)
        model.name = "PanelTestModel"

        # Create MultiIndex with market, commodity, and date
        markets = ['Market1', 'Market2']
        commodities = ['Rice', 'Wheat']
        dates = pd.date_range('2020-01-01', periods=50, freq='D')

        # Create all combinations
        index_tuples = [(m, c, d) for m in markets for c in commodities for d in dates]
        index = pd.MultiIndex.from_tuples(index_tuples, names=['market', 'commodity', 'date'])

        # Create residuals with this index
        np.random.seed(42)  # For reproducibility
        residuals = pd.Series(
            np.random.normal(0, 1, len(index)),
            index=index
        )

        model.get_residuals.return_value = residuals
        model.get_results.return_value = Mock()
        return model

    @pytest.fixture
    def sample_threshold_data(self):
        """Create sample data for threshold search plotting."""
        np.random.seed(42)
        threshold_values = np.linspace(-2, 2, 100)
        # Generate a parabola-like SSR with minimum around 0.5
        ssr_values = 5 + 2 * (threshold_values - 0.5)**2 + np.random.normal(0, 0.1, 100)
        optimal_threshold = 0.5
        confidence_interval = (0.3, 0.7)
        return threshold_values, ssr_values, optimal_threshold, confidence_interval

    @pytest.fixture
    def sample_parameter_evolution(self):
        """Create sample data for parameter evolution plotting."""
        np.random.seed(42)
        dates = pd.date_range('2020-01-01', periods=50, freq='M')

        # Create parameter paths for multiple markets
        param_paths = {
            'Market1': 0.5 + 0.1 * np.sin(np.linspace(0, 4*np.pi, 50)) + np.random.normal(0, 0.05, 50),
            'Market2': 0.3 + 0.2 * np.cos(np.linspace(0, 4*np.pi, 50)) + np.random.normal(0, 0.05, 50),
            'Market3': 0.7 - 0.1 * np.linspace(0, 1, 50) + np.random.normal(0, 0.05, 50)
        }

        return param_paths, dates

    # Tests for plot_diagnostic_suite
    def test_plot_diagnostic_suite_basic(self, mock_model, setup_and_teardown):
        """Test basic functionality of diagnostic suite plotting."""
        # Call the function
        plot_diagnostic_suite(mock_model)

        # Check that pyplot was called correctly
        assert plt.show.called

        # Check model.get_residuals was called
        mock_model.get_residuals.assert_called_once()

    def test_plot_diagnostic_suite_save(self, mock_model, setup_and_teardown):
        """Test saving diagnostic plots to file."""
        with tempfile.TemporaryDirectory() as tmpdirname:
            save_path = Path(tmpdirname) / "diagnostic_plot.png"

            # Call function with save path
            plot_diagnostic_suite(mock_model, save_path=save_path)

            # Check savefig was called instead of show
            assert plt.savefig.called
            assert not plt.show.called

    def test_plot_diagnostic_suite_panel_data(self, mock_panel_model, setup_and_teardown):
        """Test diagnostic plots with panel data."""
        # Call the function with panel data model
        plot_diagnostic_suite(mock_panel_model)

        # Check that pyplot was called correctly
        assert plt.show.called

        # Check model.get_residuals was called
        mock_panel_model.get_residuals.assert_called_once()

    def test_plot_diagnostic_suite_error_handling(self, setup_and_teardown):
        """Test error handling when model.get_residuals fails."""
        # Create model that raises exception when get_residuals is called
        model = Mock(spec=BaseThreeTierModel)
        model.name = "ErrorModel"
        model.get_residuals.side_effect = ValueError("Test error")

        # Call function - should handle exception gracefully
        plot_diagnostic_suite(model)

        # Function should return without error
        model.get_residuals.assert_called_once()

    def test_plot_diagnostic_suite_no_multiindex(self, setup_and_teardown):
        """Test with non-MultiIndex residuals."""
        # Create model that returns simple Series without MultiIndex
        model = Mock(spec=BaseThreeTierModel)
        model.name = "SimpleModel"
        model.get_residuals.return_value = pd.Series(
            np.random.normal(0, 1, 100),
            index=range(100)
        )

        # Call function
        plot_diagnostic_suite(model)

        # Should run without error
        assert plt.show.called

    # Tests for plot_residual_analysis
    def test_plot_residual_analysis_basic(self, setup_and_teardown):
        """Test basic functionality of residual analysis plotting."""
        # Create test residuals
        np.random.seed(42)
        residuals = pd.Series(
            np.random.normal(0, 1, 100),
            index=pd.date_range('2020-01-01', periods=100, freq='D')
        )

        # Call function
        plot_residual_analysis(residuals, model_name="Test Model")

        # Check plotting happened
        assert plt.show.called

    def test_plot_residual_analysis_panel_data(self, setup_and_teardown):
        """Test residual analysis with panel data."""
        # Create MultiIndex with market, commodity, and date
        markets = ['Market1']
        commodities = ['Rice']
        dates = pd.date_range('2020-01-01', periods=50, freq='D')

        # Create all combinations
        index_tuples = [(m, c, d) for m in markets for c in commodities for d in dates]
        index = pd.MultiIndex.from_tuples(index_tuples, names=['market', 'commodity', 'date'])

        # Create residuals with this index
        np.random.seed(42)
        residuals = pd.Series(
            np.random.normal(0, 1, len(index)),
            index=index
        )

        # Call function
        plot_residual_analysis(residuals, model_name="Panel Model")

        # Check plotting happened
        assert plt.show.called

    def test_plot_residual_analysis_save(self, setup_and_teardown):
        """Test saving residual analysis plots to file."""
        # Create test residuals
        np.random.seed(42)
        residuals = pd.Series(
            np.random.normal(0, 1, 100),
            index=pd.date_range('2020-01-01', periods=100, freq='D')
        )

        with tempfile.TemporaryDirectory() as tmpdirname:
            save_path = Path(tmpdirname) / "residual_analysis.png"

            # Call function with save path
            plot_residual_analysis(residuals, model_name="Test Model", save_path=save_path)

            # Check savefig was called instead of show
            assert plt.savefig.called
            assert not plt.show.called

    def test_plot_residual_analysis_empty_series(self, setup_and_teardown):
        """Test behavior with empty residuals series."""
        # Create empty series
        residuals = pd.Series([], dtype=float)

        # Call function - should handle empty series gracefully
        plot_residual_analysis(residuals, model_name="Empty Model")

        # Function should still run without error
        assert plt.show.called

    # Tests for plot_threshold_search
    def test_plot_threshold_search_basic(self, sample_threshold_data, setup_and_teardown):
        """Test basic functionality of threshold search plotting."""
        # Unpack the sample data
        threshold_values, ssr_values, optimal_threshold, confidence_interval = sample_threshold_data

        # Call function
        plot_threshold_search(threshold_values, ssr_values, optimal_threshold, confidence_interval)

        # Check plotting happened
        assert plt.show.called

    def test_plot_threshold_search_save(self, sample_threshold_data, setup_and_teardown):
        """Test saving threshold search plots to file."""
        # Unpack the sample data
        threshold_values, ssr_values, optimal_threshold, confidence_interval = sample_threshold_data

        with tempfile.TemporaryDirectory() as tmpdirname:
            save_path = Path(tmpdirname) / "threshold_search.png"

            # Call function with save path
            plot_threshold_search(
                threshold_values, ssr_values, optimal_threshold,
                confidence_interval, save_path=save_path
            )

            # Check savefig was called instead of show
            assert plt.savefig.called
            assert not plt.show.called

    def test_plot_threshold_search_edge_cases(self, setup_and_teardown):
        """Test threshold search plotting with edge cases."""
        # Case 1: Flat SSR (no clear minimum)
        threshold_values = np.linspace(-2, 2, 100)
        ssr_values = np.ones_like(threshold_values)  # Constant SSR
        optimal_threshold = 0.0
        confidence_interval = (-1.0, 1.0)

        # Call function
        plot_threshold_search(threshold_values, ssr_values, optimal_threshold, confidence_interval)

        # Function should still run
        assert plt.show.called
        plt.close('all')

        # Case 2: Single value
        threshold_values = np.array([0.5])
        ssr_values = np.array([1.0])
        optimal_threshold = 0.5
        confidence_interval = (0.5, 0.5)

        # Call function
        plot_threshold_search(threshold_values, ssr_values, optimal_threshold, confidence_interval)

        # Function should handle this special case
        assert plt.show.called

    # Tests for plot_parameter_evolution
    def test_plot_parameter_evolution_basic(self, sample_parameter_evolution, setup_and_teardown):
        """Test basic functionality of parameter evolution plotting."""
        # Unpack the sample data
        param_paths, dates = sample_parameter_evolution

        # Call function
        plot_parameter_evolution(param_paths, dates, param_name="Beta")

        # Check plotting happened
        assert plt.show.called

    def test_plot_parameter_evolution_save(self, sample_parameter_evolution, setup_and_teardown):
        """Test saving parameter evolution plots to file."""
        # Unpack the sample data
        param_paths, dates = sample_parameter_evolution

        with tempfile.TemporaryDirectory() as tmpdirname:
            save_path = Path(tmpdirname) / "parameter_evolution.png"

            # Call function with save path
            plot_parameter_evolution(param_paths, dates, param_name="Beta", save_path=save_path)

            # Check savefig was called instead of show
            assert plt.savefig.called
            assert not plt.show.called

    def test_plot_parameter_evolution_single_market(self, setup_and_teardown):
        """Test parameter evolution with just one market."""
        np.random.seed(42)
        dates = pd.date_range('2020-01-01', periods=50, freq='M')

        # Create parameter path for a single market
        param_paths = {
            'Market1': 0.5 + 0.1 * np.sin(np.linspace(0, 4*np.pi, 50)) + np.random.normal(0, 0.05, 50)
        }

        # Call function
        plot_parameter_evolution(param_paths, dates, param_name="Alpha")

        # Check plotting happened
        assert plt.show.called

    def test_plot_parameter_evolution_empty_dict(self, setup_and_teardown):
        """Test parameter evolution with empty parameter dictionary."""
        dates = pd.date_range('2020-01-01', periods=50, freq='M')

        # Empty parameter paths
        param_paths = {}

        # Call function - should handle empty dict gracefully
        plot_parameter_evolution(param_paths, dates, param_name="Gamma")

        # Function should still run without error
        assert plt.show.called

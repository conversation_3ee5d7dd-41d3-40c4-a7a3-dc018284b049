"""
Econometric Test Implementations for Panel Data.

This module provides implementations of various econometric tests
suitable for panel data, designed to work with the `DiagnosticAdapter`.
"""

from typing import Tuple, Any, Dict, Optional
import pandas as pd
import numpy as np
from statsmodels.tsa.stattools import adfuller
import statsmodels.api as sm
from scipy import stats
try:
    from linearmodels.panel.unitroot import ImPesaranShin
    HAS_LINEARMODELS_UNITROOT = True
except ImportError:
    HAS_LINEARMODELS_UNITROOT = False
    ImPesaranShin = None

from yemen_market.utils.logging import info, warning, error

# PanelInfo typically would be a TypedDict or a dataclass
# For now, we'll assume it's a Dict[str, Any]
PanelInfo = Dict[str, Any]


def wooldridge_serial_correlation(
    residuals: pd.Series, panel_info: PanelInfo
) -> Tuple[float, float, str]:
    """
    Wooldridge test for serial correlation in panel data (<PERSON><PERSON>ker 2003 implementation).

    This implements the exact specification from <PERSON><PERSON><PERSON> (2003) "Testing for serial
    correlation in linear panel-data models" which is used in Stata's xtserial command.
    The test is designed for fixed effects models and tests for AR(1) serial correlation
    in the idiosyncratic errors.

    Parameters
    ----------
    residuals : pd.Series
        The residuals from the panel model (preferably from a fixed effects model),
        indexed by entity and time.
    panel_info : PanelInfo
        A dictionary containing panel structure information.

    Returns
    -------
    Tuple[float, float, str]
        F-statistic, p-value, and a recommendation string.

    References
    ----------
    Drukker, D. M. (2003). Testing for serial correlation in linear panel-data models.
    The Stata Journal, 3(2), 168-177.

    Wooldridge, J. M. (2002). Econometric Analysis of Cross Section and Panel Data.
    MIT Press, Chapter 10.5.4.
    """
    info("Running Wooldridge test for serial correlation (Drukker 2003 specification)...")
    entity_id = panel_info.get("entity_id", "entity")
    time_id = panel_info.get("time_id", "time")
    N = panel_info.get("N")

    # Validate inputs
    if not isinstance(residuals.index, pd.MultiIndex):
        error("Wooldridge test requires residuals with a MultiIndex (entity, time).")
        return np.nan, np.nan, "Error: Residuals must have MultiIndex."

    if N is None or N < 2:
        error("Wooldridge test requires at least 2 entities.")
        return np.nan, np.nan, "Error: Need at least 2 entities for test."

    # Create DataFrame and ensure proper sorting
    df = residuals.reset_index(name='resid')
    df = df.sort_values(by=[entity_id, time_id])

    # Step 1: First-difference the residuals within each entity
    # This is the key difference from the simplified version - we use first differences
    df['resid_diff'] = df.groupby(entity_id)['resid'].diff()
    df['resid_diff_lag1'] = df.groupby(entity_id)['resid_diff'].shift(1)

    # Drop missing values from differencing and lagging
    df_clean = df.dropna(subset=['resid_diff', 'resid_diff_lag1'])

    if len(df_clean) < 10:  # Arbitrary minimum for meaningful test
        warning("Not enough observations for Wooldridge test after differencing.")
        return np.nan, np.nan, "Not enough data for test (need at least 10 obs after differencing)."

    # Count observations per entity after cleaning
    obs_per_entity = df_clean.groupby(entity_id).size()
    entities_with_data = len(obs_per_entity)

    if entities_with_data < 2:
        warning("Less than 2 entities have sufficient data after differencing.")
        return np.nan, np.nan, "Not enough entities with data after differencing."

    # Step 2: Run the auxiliary regression
    # Regress first-differenced residuals on their lags
    # Under H0 of no serial correlation, the coefficient should be -0.5
    X = sm.add_constant(df_clean['resid_diff_lag1'])
    y = df_clean['resid_diff']

    try:
        # Run OLS regression
        aux_model = sm.OLS(y, X).fit()

        # Get coefficient on lagged first-differenced residuals
        rho_hat = aux_model.params['resid_diff_lag1']
        se_rho = aux_model.bse['resid_diff_lag1']

        # Test H0: rho = -0.5 (null of no serial correlation in levels)
        # This is the key insight from Wooldridge/Drukker
        t_stat = (rho_hat - (-0.5)) / se_rho

        # Calculate F-statistic (t^2 for single restriction)
        f_stat = t_stat ** 2

        # Degrees of freedom
        df1 = 1  # One restriction
        df2 = len(df_clean) - 2  # n - k where k=2 (constant and one regressor)

        # Calculate p-value from F-distribution
        p_value = 1 - stats.f.cdf(f_stat, df1, df2)

        # Additional diagnostics
        info(f"Wooldridge test: rho_hat = {rho_hat:.4f} (H0: rho = -0.5)")
        info(f"Number of entities used: {entities_with_data}, Total obs: {len(df_clean)}")

        # Recommendation based on results
        recommendation = ""
        if p_value <= 0.01:
            recommendation = (
                "Strong evidence of serial correlation (p < 0.01). "
                "Use cluster-robust standard errors or Driscoll-Kraay standard errors. "
                "Consider dynamic panel models (e.g., Arellano-Bond) if appropriate."
            )
        elif p_value <= 0.05:
            recommendation = (
                "Evidence of serial correlation (p < 0.05). "
                "Use cluster-robust standard errors at minimum. "
                "Consider Driscoll-Kraay SEs if cross-sectional dependence is also present."
            )
        elif p_value <= 0.10:
            recommendation = (
                "Weak evidence of serial correlation (p < 0.10). "
                "Consider using cluster-robust standard errors as a precaution."
            )
        else:
            recommendation = (
                "No significant evidence of serial correlation. "
                "Standard errors from the model are likely reliable, "
                "but cluster-robust SEs are still recommended for panel data."
            )

        return f_stat, p_value, recommendation

    except Exception as e:
        error(f"Error in Wooldridge test auxiliary regression: {e}")
        return np.nan, np.nan, f"Error during test: {e}"


def pesaran_cd_test(
    residuals: pd.Series, panel_info: PanelInfo
) -> Tuple[float, float, str]:
    """
    Pesaran (2004, 2015) test for cross-sectional dependence in panels.

    Parameters
    ----------
    residuals : pd.Series
        The residuals from the panel model, indexed by entity and time.
    panel_info : PanelInfo
        A dictionary containing panel structure information. (See wooldridge_serial_correlation)

    Returns
    -------
    Tuple[float, float, str]
        Test statistic, p-value, and a recommendation string.
    """
    info("Running Pesaran CD test for cross-sectional dependence...")
    entity_id = panel_info.get("entity_id", "entity")
    time_id = panel_info.get("time_id", "time")
    N = panel_info.get("N")
    T_avg = panel_info.get("nobs") / N if N else 0 # Average T

    if not isinstance(residuals.index, pd.MultiIndex):
        error("Pesaran CD test requires residuals with a MultiIndex (entity, time).")
        return np.nan, np.nan, "Error: Residuals must have MultiIndex."

    if N is None or N < 2 or T_avg < 3: # Need at least 2 entities and some time periods
        warning("Not enough entities or time periods for Pesaran CD test.")
        return np.nan, np.nan, "Not enough data for test (N<2 or T_avg<3)."

    # Reshape residuals to wide format (T x N)
    # Pivot the table: index=time, columns=entity, values=residuals
    resid_df = residuals.reset_index()
    try:
        wide_residuals = resid_df.pivot(index=time_id, columns=entity_id, values=residuals.name)
    except Exception as e:
        error(f"Could not pivot residuals for Pesaran CD test: {e}")
        # This can happen if there are duplicate (entity, time) pairs
        # Or if residuals.name is None
        if residuals.name is None: # Try a default name
             wide_residuals = resid_df.pivot(index=time_id, columns=entity_id, values='resid')
        else:
            return np.nan, np.nan, f"Error pivoting data: {e}"


    # Drop entities with all NaNs (if any, due to unbalanced panel)
    wide_residuals = wide_residuals.dropna(axis=1, how='all')

    # Drop time periods where all entities have NaNs
    wide_residuals = wide_residuals.dropna(axis=0, how='all')

    N_actual = wide_residuals.shape[1]
    if N_actual < 2:
        warning("Not enough entities with non-missing residuals for Pesaran CD test.")
        return np.nan, np.nan, "Not enough entities after handling NaNs."

    # Calculate pairwise correlations of residuals
    # For each pair of entities (i, j), calculate corr(resid_i, resid_j)
    # Only use time periods where both entities have non-missing residuals

    sum_rho_ij = 0
    num_pairs = 0

    entities = wide_residuals.columns
    for i in range(N_actual):
        for j in range(i + 1, N_actual):
            resid_i = wide_residuals[entities[i]]
            resid_j = wide_residuals[entities[j]]

            # Common non-NaN observations
            common_mask = ~resid_i.isnull() & ~resid_j.isnull()
            if common_mask.sum() < 2: # Need at least 2 common obs for correlation
                continue

            rho_ij = np.corrcoef(resid_i[common_mask], resid_j[common_mask])[0, 1]
            sum_rho_ij += rho_ij
            num_pairs += 1

    if num_pairs == 0:
        warning("No valid pairs for correlation in Pesaran CD test.")
        return np.nan, np.nan, "No valid pairs for correlation."

    # Pesaran CD statistic for unbalanced panels (Pesaran, 2015, Eq. 9)
    # CD = sqrt(2 / (N(N-1))) * sum_{i=1}^{N-1} sum_{j=i+1}^{N} (sqrt(T_ij) * rho_ij)
    # where T_ij is the number of common observations for units i and j.

    sum_sqrt_Tij_rho_ij = 0
    # num_pairs was calculated in the previous loop

    # Recalculate sum_rho_ij by iterating and summing sqrt(T_ij) * rho_ij
    # The previous loop correctly calculated num_pairs and sum_rho_ij (though sum_rho_ij is not directly used in the unbalanced formula)
    # We need sum_sqrt_Tij_rho_ij

    # Re-initialize for clarity in this block
    sum_sqrt_Tij_rho_ij = 0
    actual_pairs_for_cd = 0 # Count pairs that contribute to the sum

    for i in range(N_actual):
        for j in range(i + 1, N_actual):
            resid_i = wide_residuals[entities[i]]
            resid_j = wide_residuals[entities[j]]

            common_mask = ~resid_i.isnull() & ~resid_j.isnull()
            T_ij = common_mask.sum()

            if T_ij < 2: # Need at least 2 common obs for correlation
                continue

            rho_ij = np.corrcoef(resid_i[common_mask], resid_j[common_mask])[0, 1]

            if np.isnan(rho_ij): # Handle cases where correlation might be NaN (e.g. zero variance in one series for the common period)
                warning(f"NaN correlation for pair ({entities[i]}, {entities[j]}). Skipping this pair for CD test.")
                continue

            sum_sqrt_Tij_rho_ij += np.sqrt(T_ij) * rho_ij
            actual_pairs_for_cd +=1

    if actual_pairs_for_cd == 0: # Check if any pairs contributed
        warning("No valid pairs contributed to the Pesaran CD statistic sum.")
        return np.nan, np.nan, "No valid pairs for CD statistic sum."

    cd_statistic = np.sqrt(2 / (N_actual * (N_actual - 1))) * sum_sqrt_Tij_rho_ij

    # The CD statistic is asymptotically N(0,1) under H0 (no cross-sectional dependence)
    p_value = 2 * (1 - stats.norm.cdf(np.abs(cd_statistic)))

    recommendation = ""
    if p_value <= 0.05:
        recommendation = "Evidence of cross-sectional dependence. Consider Driscoll-Kraay standard errors or spatial panel models."
    else:
        recommendation = "No significant evidence of cross-sectional dependence."

    return cd_statistic, p_value, recommendation


def ips_unit_root_test(
    series: pd.Series, panel_info: PanelInfo
) -> Tuple[float, float, str]:
    """
    Im-Pesaran-Shin (2003) panel unit root test.

    Parameters
    ----------
    series : pd.Series
        The series to test for unit roots, indexed by entity and time.
    panel_info : PanelInfo
        A dictionary containing panel structure information.

    Returns
    -------
    Tuple[float, float, str]
        Test statistic, p-value, and a recommendation string.
    """
    info("Running Im-Pesaran-Shin (IPS) unit root test...")
    entity_id = panel_info.get("entity_id", "entity")
    # time_id = panel_info.get("time_id", "time") # Not directly used here but good for context
    entities = panel_info.get("entities")

    if not isinstance(series.index, pd.MultiIndex):
        error("IPS test requires series with a MultiIndex (entity, time).")
        return np.nan, np.nan, "Error: Series must have MultiIndex."

    if entities is None or len(entities) == 0:
        warning("No entities found for IPS test.")
        return np.nan, np.nan, "No entities for test."

    adf_t_stats = []
    valid_entities = 0

    for entity in entities:
        try:
            # Extract series for the current entity
            entity_series = series.xs(entity, level=entity_id).dropna()
            if len(entity_series) < 10: # Minimum observations for ADF
                warning(f"Skipping entity {entity} for IPS test: too few observations ({len(entity_series)}).")
                continue

            # Run ADF test for this entity
            # Using 'AIC' for lag selection, no constant/trend by default for IPS
            # H0: series has a unit root
            adf_result = adfuller(entity_series, regression='c', autolag='AIC') # 'c' for constant
            adf_t_stats.append(adf_result[0]) # ADF statistic
            valid_entities +=1
        except KeyError:
            warning(f"Entity {entity} not found in series for IPS test.")
        except Exception as e:
            error(f"Error running ADF for entity {entity} in IPS test: {e}")
            # Optionally, append np.nan or skip

    if valid_entities == 0:
        warning("No valid ADF tests could be performed for any entity.")
        return np.nan, np.nan, "No valid ADF tests for any entity."

    # Calculate average of ADF t-statistics
    t_bar = np.mean(adf_t_stats)

    # IPS statistic requires expected mean and variance of ADF t-stats under H0.
    # Ensure the input series has a MultiIndex with entity and time
    # The entity_id and time_id from panel_info are names of the index levels
    entity_col_name = panel_info.get("entity_id", "entity")
    time_col_name = panel_info.get("time_id", "time")

    if not isinstance(series.index, pd.MultiIndex) or \
       not {entity_col_name, time_col_name}.issubset(set(series.index.names)):
        error(f"IPS test requires series with a MultiIndex named '{entity_col_name}' and '{time_col_name}'.")
        return np.nan, np.nan, f"Error: Series must have MultiIndex named '{entity_col_name}', '{time_col_name}'."

    # linearmodels expects the entity to be the first level and time the second.
    # If not, we might need to reorder. Assuming it's already correctly ordered.
    # Also, the series should be a DataFrame with the time series as a column.

    # Convert series to DataFrame if it's not already, and ensure correct index names
    # The ImPesaranShin expects a DataFrame where the index is (entity, time)
    # and the data is in a single column.

    # Ensure the series is properly named for conversion to DataFrame column
    if series.name is None:
        series = series.rename("value_for_ips") # Give it a default name

    # Convert to DataFrame
    df_for_ips = series.to_frame()

    # Check if linearmodels unitroot module is available
    if not HAS_LINEARMODELS_UNITROOT:
        warning("linearmodels.panel.unitroot not available. Using manual IPS calculation.")
        # Fall back to manual calculation using individual ADF tests
        # This is already done above, so we'll use those results

        # For a simplified IPS test, we can use the average t-statistic
        # and compare to critical values from Im, Pesaran, and Shin (2003)
        # This is a simplified version - the full test requires adjustment for
        # the number of lags used in each ADF test

        if len(adf_t_stats) == 0:
            return np.nan, np.nan, "No valid ADF statistics for IPS test."

        # Average t-statistic
        t_bar = np.mean(adf_t_stats)

        # For a rough approximation, we can use the fact that under H0,
        # the standardized t-bar statistic is approximately N(0,1)
        # This is simplified - actual IPS test has specific adjustments

        # Using simplified critical values for illustration
        # In practice, you'd need the exact IPS critical values table
        if t_bar < -2.5:  # Very rough approximation
            p_value_approx = 0.01
            recommendation = "Strong evidence against unit roots (simplified test)."
        elif t_bar < -2.0:
            p_value_approx = 0.05
            recommendation = "Evidence against unit roots (simplified test)."
        else:
            p_value_approx = 0.10
            recommendation = "Cannot reject unit roots (simplified test). Consider differencing."

        return t_bar, p_value_approx, recommendation

    try:
        # Initialize ImPesaranShin test
        # trend='c' includes an individual-specific intercept (demeaning)
        # lags=None lets the model select lags using AIC by default (up to a max)
        # A specific number of lags can also be provided, e.g., lags=2
        ips_test_instance = ImPesaranShin(df_for_ips[series.name], trend='c', lags=None, test_type='t-stat')

        ips_statistic = ips_test_instance.statistic
        ips_p_value = ips_test_instance.pvalue

        # The null hypothesis of ImPesaranShin is that all series have a unit root.
        # A small p-value means we reject H0, suggesting stationarity for at least some series.
        recommendation = ""
        if ips_p_value <= 0.05:
            recommendation = "Evidence against unit roots (some series may be stationary)."
        else:
            recommendation = "Cannot reject unit roots (simplified test). Consider differencing."

        return ips_statistic, ips_p_value, recommendation

    except Exception as e:
        error(f"Error running ImPesaranShin test using linearmodels: {e}")
        # This could be due to various issues: insufficient data per panel, all NaN series, etc.
        # linearmodels itself has checks for these.
        return np.nan, np.nan, f"Error during IPS test execution: {e}"

def modified_wald_heteroskedasticity(
    residuals: pd.Series,
    panel_info: PanelInfo,
    fitted_values: Optional[pd.Series] = None
) -> Tuple[float, float, str]:
    """
    Modified Wald test for groupwise heteroskedasticity in panel data.

    This test checks whether the variance of errors differs across entities
    (cross-sectional units). It's particularly important for fixed effects models.
    The null hypothesis is homoskedasticity (equal variances across groups).

    Parameters
    ----------
    residuals : pd.Series
        The residuals from the panel model, indexed by entity and time.
    panel_info : PanelInfo
        A dictionary containing panel structure information.
    fitted_values : pd.Series, optional
        Fitted values from the model (not used in basic version but kept for API consistency).

    Returns
    -------
    Tuple[float, float, str]
        Chi-squared statistic, p-value, and a recommendation string.

    References
    ----------
    Greene, W. H. (2003). Econometric Analysis (5th ed.). Prentice Hall.
    Baum, C. F. (2001). Residual diagnostics for cross-section time series regression models.
    The Stata Journal, 1(1), 101-104.
    """
    info("Running Modified Wald test for groupwise heteroskedasticity...")
    entity_id = panel_info.get("entity_id", "entity")
    time_id = panel_info.get("time_id", "time")
    N = panel_info.get("N")

    # Validate inputs
    if not isinstance(residuals.index, pd.MultiIndex):
        error("Modified Wald test requires residuals with a MultiIndex (entity, time).")
        return np.nan, np.nan, "Error: Residuals must have MultiIndex."

    if N is None or N < 2:
        error("Modified Wald test requires at least 2 entities.")
        return np.nan, np.nan, "Error: Need at least 2 entities for test."

    # Create DataFrame from residuals
    df = residuals.reset_index(name='resid')
    df = df.sort_values(by=[entity_id, time_id])

    # Calculate group-specific variances
    group_variances = df.groupby(entity_id)['resid'].var()
    group_counts = df.groupby(entity_id).size()

    # Remove groups with insufficient observations
    valid_groups = group_counts[group_counts >= 2].index
    group_variances = group_variances[valid_groups]
    group_counts = group_counts[valid_groups]

    N_valid = len(valid_groups)
    if N_valid < 2:
        warning("Less than 2 entities have sufficient observations for variance calculation.")
        return np.nan, np.nan, "Not enough entities with sufficient data."

    # Calculate the overall variance (under H0 of homoskedasticity)
    overall_variance = df[df[entity_id].isin(valid_groups)]['resid'].var()

    # Modified Wald statistic
    # W = sum_i (T_i * (sigma_i^2 / sigma^2 - 1)^2)
    # where sigma_i^2 is the variance for group i and sigma^2 is the pooled variance

    # For the exact test, we need to use the likelihood ratio approach
    # LR = sum_i [T_i * log(sigma^2 / sigma_i^2) + (T_i - 1) * (sigma_i^2 / sigma^2 - 1)]

    # Simplified version using chi-squared approximation
    # Under H0, each (sigma_i^2 / sigma^2) follows a chi-squared distribution

    # Calculate test statistic
    chi2_stat = 0
    for entity in valid_groups:
        T_i = group_counts[entity]
        sigma_i_sq = group_variances[entity]

        # Contribution to test statistic
        # Using the fact that (T_i - 1) * sigma_i^2 / sigma^2 ~ chi2(T_i - 1)
        chi2_stat += (T_i - 1) * (sigma_i_sq / overall_variance - 1)**2

    # Degrees of freedom
    df = N_valid - 1

    # Calculate p-value
    p_value = 1 - stats.chi2.cdf(chi2_stat, df)

    # Additional diagnostics
    info(f"Modified Wald test: Chi2({df}) = {chi2_stat:.4f}")
    info(f"Number of entities tested: {N_valid}")
    info(f"Variance ratio range: [{group_variances.min()/overall_variance:.2f}, {group_variances.max()/overall_variance:.2f}]")

    # Recommendations
    recommendation = ""
    if p_value <= 0.01:
        recommendation = (
            "Strong evidence of groupwise heteroskedasticity (p < 0.01). "
            "Use robust standard errors clustered by entity. "
            "Consider weighted least squares or feasible GLS if efficiency is important."
        )
    elif p_value <= 0.05:
        recommendation = (
            "Evidence of groupwise heteroskedasticity (p < 0.05). "
            "Use robust standard errors clustered by entity."
        )
    elif p_value <= 0.10:
        recommendation = (
            "Weak evidence of groupwise heteroskedasticity (p < 0.10). "
            "Consider using robust standard errors as a precaution."
        )
    else:
        recommendation = (
            "No significant evidence of groupwise heteroskedasticity. "
            "Standard errors from the model are likely reliable, "
            "but robust SEs are still recommended for panel data."
        )

    return chi2_stat, p_value, recommendation


def breusch_pagan_lm_test(
    residuals: pd.Series,
    panel_info: PanelInfo,
    scaled: bool = True
) -> Tuple[float, float, str]:
    """
    Breusch-Pagan LM test for cross-sectional dependence (alternative to Pesaran CD).

    This test is more suitable for panels where N is small relative to T.
    The null hypothesis is no cross-sectional dependence.

    Parameters
    ----------
    residuals : pd.Series
        The residuals from the panel model, indexed by entity and time.
    panel_info : PanelInfo
        A dictionary containing panel structure information.
    scaled : bool, default=True
        If True, use the scaled version of the test (better for finite samples).

    Returns
    -------
    Tuple[float, float, str]
        Test statistic, p-value, and a recommendation string.

    References
    ----------
    Breusch, T. S., & Pagan, A. R. (1980). The Lagrange multiplier test and its
    applications to model specification in econometrics. Review of Economic Studies, 47(1), 239-253.
    """
    info("Running Breusch-Pagan LM test for cross-sectional dependence...")
    entity_id = panel_info.get("entity_id", "entity")
    time_id = panel_info.get("time_id", "time")
    N = panel_info.get("N")

    if not isinstance(residuals.index, pd.MultiIndex):
        error("Breusch-Pagan LM test requires residuals with a MultiIndex (entity, time).")
        return np.nan, np.nan, "Error: Residuals must have MultiIndex."

    if N is None or N < 2:
        warning("Not enough entities for Breusch-Pagan LM test.")
        return np.nan, np.nan, "Not enough entities for test (N<2)."

    # Reshape residuals to wide format
    resid_df = residuals.reset_index()
    try:
        wide_residuals = resid_df.pivot(index=time_id, columns=entity_id, values=residuals.name or 'resid')
    except Exception as e:
        error(f"Could not pivot residuals for Breusch-Pagan LM test: {e}")
        return np.nan, np.nan, f"Error pivoting data: {e}"

    # Drop entities/times with all NaNs
    wide_residuals = wide_residuals.dropna(axis=1, how='all')
    wide_residuals = wide_residuals.dropna(axis=0, how='all')

    N_actual = wide_residuals.shape[1]
    if N_actual < 2:
        warning("Not enough entities with non-missing residuals for Breusch-Pagan LM test.")
        return np.nan, np.nan, "Not enough entities after handling NaNs."

    # Calculate correlation matrix of residuals
    corr_matrix = wide_residuals.corr()

    # LM statistic is based on sum of squared correlations
    lm_stat = 0
    T_avg = 0
    num_pairs = 0

    entities = wide_residuals.columns
    for i in range(N_actual):
        for j in range(i + 1, N_actual):
            rho_ij_sq = corr_matrix.iloc[i, j]**2

            if not np.isnan(rho_ij_sq):
                # Count common observations for this pair
                resid_i = wide_residuals[entities[i]]
                resid_j = wide_residuals[entities[j]]
                common_mask = ~resid_i.isnull() & ~resid_j.isnull()
                T_ij = common_mask.sum()

                if T_ij >= 2:  # Need at least 2 observations
                    lm_stat += T_ij * rho_ij_sq
                    T_avg += T_ij
                    num_pairs += 1

    if num_pairs == 0:
        warning("No valid pairs for Breusch-Pagan LM test.")
        return np.nan, np.nan, "No valid pairs for test."

    T_avg = T_avg / num_pairs if num_pairs > 0 else 0

    if scaled:
        # Scaled version (Pesaran et al., 2008)
        lm_stat_scaled = np.sqrt(1 / (N_actual * (N_actual - 1))) * (lm_stat - num_pairs)
        # Under H0, asymptotically N(0,1)
        p_value = 2 * (1 - stats.norm.cdf(np.abs(lm_stat_scaled)))
        test_stat = lm_stat_scaled
    else:
        # Original version - chi-squared with N(N-1)/2 degrees of freedom
        df = N_actual * (N_actual - 1) / 2
        p_value = 1 - stats.chi2.cdf(lm_stat, df)
        test_stat = lm_stat

    # Recommendations
    recommendation = ""
    if p_value <= 0.01:
        recommendation = (
            "Strong evidence of cross-sectional dependence (p < 0.01). "
            "Use Driscoll-Kraay standard errors or spatial panel models. "
            "Consider common correlated effects (CCE) estimators."
        )
    elif p_value <= 0.05:
        recommendation = (
            "Evidence of cross-sectional dependence (p < 0.05). "
            "Use Driscoll-Kraay standard errors or consider spatial correlation."
        )
    else:
        recommendation = "No significant evidence of cross-sectional dependence."

    return test_stat, p_value, recommendation


# Future tests to implement:
# - Hausman test (for FE vs RE specification)
# - Sargan-Hansen test (for overidentifying restrictions in IV models)
# - Weak instrument diagnostics (for IV models)
# - Panel normality tests (Jarque-Bera for panels)
# - Panel stationarity tests (Hadri, LLC, Fisher-type)

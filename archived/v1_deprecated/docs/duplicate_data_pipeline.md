# Technical Specifications - Data Pipeline & Models

## Core Analysis Components

### 1. Data Pipeline Architecture

- **Sources**: HDX, WFP, ACLED, ACAPS, Sana'a Center
- **Key datasets**:
  - WFP Yemen food prices (includes market-level exchange rates) ✓ Confirmed
  - ACAPS areas of control (bi-weekly territorial updates)
  - ACLED conflict events (aggregated only)
  - HDX administrative boundaries
- **Implementation Order**:
  1. HDXClient → 2. WFPProcessor → 3. ACAPSProcessor → 4. SpatialJoins → 5. PanelBuilder

### 2. Econometric Models (Weeks 5-6)

1. **Time-Varying Parameter VECM** (Bayesian) - Primary model
2. **Multiple Threshold VECM** (<PERSON>-<PERSON>o) - Non-linear adjustments
3. **Momentum TAR** (<PERSON><PERSON>-<PERSON><PERSON><PERSON>) - Asymmetric adjustment
4. **Spatial Econometric Models** - Geographic dependencies
5. **Markov Regime-Switching** - Conflict intensity regimes

### 3. Diagnostic Tests Required (Weeks 7-8)

- Unit root: ADF-<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, KPSS
- Cointegration: <PERSON><PERSON>, <PERSON><PERSON><PERSON>
- Structural breaks: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
- <PERSON><PERSON>: <PERSON>'s <PERSON>, spatial HAC
- Robustness: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>

### 4. Policy Simulations (Weeks 9-10)

- Exchange rate unification scenarios
- Market connectivity improvements
- Dynamic transition paths with uncertainty quantification

## Key Implementation Details

### Data Processing

- Markets mapped to control zones via spatial joins (geopandas)
- Exchange rate differentials calculated between Houthi/Government areas
- Monthly frequency (WFP data mostly monthly despite weekly target)
- Panel structure: market × time × commodity

### Model Specifications

```python
# Threshold VECM structure
n_thresholds = 2
grid_search_pct = (0.15, 0.85)
bootstrap_reps = 1000

# Spatial weights
distance_threshold = 200  # km
control_penalty = 0.1  # 90% reduction for cross-control trade
correlation_threshold = 0.8  # for network inference

# Time-varying parameters
n_chains = 4
n_samples = 2000
tune = 1000
```

### Output Requirements

1. **LaTeX Report**: Full technical analysis with Chicago-style citations
2. **Executive PowerPoint**: Policy-focused summary
3. **Reproducible Pipeline**: Make commands for full workflow
4. **Publication-ready Figures**: Spatial maps, time series, policy simulations

## Implementation Priorities

### Phase 1: Data Pipeline (Weeks 1-2) ✅ COMPLETE

1. ✅ Repository setup
2. ✅ Configuration files
3. ✅ Implement `HDXClient` in `src/yemen_market/data/hdx_client.py`
4. ✅ Create `WFPProcessor` for price data with exchange rates
5. ✅ Build `ACAPSProcessor` for areas of control
6. ✅ Implement `SpatialJoiner` for market-zone mapping
7. ✅ Create `PanelBuilder` for analysis-ready datasets

### Phase 2: Core Models (Weeks 3-6)

1. Base model classes with consistent interface
2. Threshold VECM implementation
3. Time-varying parameter VECM
4. M-TAR model
5. Spatial econometric models

### Phase 3: Analysis & Reporting (Weeks 7-12)

1. Comprehensive diagnostic test suite
2. Machine learning pattern detection
3. Policy simulation framework
4. LaTeX report generation
5. Executive PowerPoint creation

## Key Data Outputs

- **Price Data**: `data/processed/wfp/wfp_price_data.parquet`
- **Exchange Rates**: `data/processed/wfp/exchange_rates.parquet`
- **Control Zones**: `data/processed/control_zones/control_zones_monthly.parquet`
- **Spatial Mapping**: `data/processed/spatial/market_zones_temporal.parquet`
- **Integrated Panels**: `data/processed/panels/integrated_panel.parquet`

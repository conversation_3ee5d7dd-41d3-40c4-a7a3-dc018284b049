# Archived Code and Documentation

**Archive Date**: May 29, 2025  
**Reason**: Three-Tier Implementation & Diagnostic Framework Migration

## Directory Structure

### v1_deprecated/
Contains modules and documentation from the pre-three-tier implementation:

- **diagnostics/**: Legacy diagnostic modules replaced by `models/three_tier/diagnostics/`
- **models/**: Old model base classes (base.py, econometric_backbone.py) from dual-track approach
- **docs/**: Documentation for deprecated track1_complex and track2_simple models

### migration_complete/
Contains completed migration artifacts:

- **reports/**: Completed migration and test resolution reports
- **prompts/**: Migration-specific prompts that are no longer needed

### placeholders/
Empty directories that were never implemented:

- **ml/**: Machine learning module placeholder
- **simulation/**: Simulation module placeholder

## Important Notes

1. All code in this archive is **deprecated** and should not be used
2. The three-tier implementation replaces all functionality here
3. Files were moved using `git mv` to preserve history
4. For migration help, see `/MIGRATION_GUIDE.md` in project root

## Migration Timeline

- **Phase 4 (January 2025)**: Dual-track to three-tier migration
- **May 29, 2025**: Diagnostic framework migration completed
- **May 29, 2025**: Files archived

## References

- Current implementation: `src/yemen_market/models/three_tier/`
- Migration guide: `/MIGRATION_GUIDE.md`
- Active documentation: `/docs/` (excluding archived sections)
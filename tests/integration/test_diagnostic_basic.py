#!/usr/bin/env python3
"""Basic test to verify diagnostic framework components work."""

import sys
import numpy as np
import pandas as pd
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from yemen_market.utils.logging import setup_logging, info, warning, error
from yemen_market.models.three_tier.diagnostics import (
    ThreeTierPanelDiagnostics,
    DiagnosticAdapter, 
    DiagnosticReport
)
from yemen_market.models.three_tier.core.results_container import ResultsContainer


def test_basic_diagnostics():
    """Test basic diagnostic functionality."""
    setup_logging()
    
    info("="*60)
    info("Testing Basic Diagnostic Components")
    info("="*60)
    
    # Create simple test data
    n_obs = 100
    data = pd.DataFrame({
        'market': ['Market_A'] * 50 + ['Market_B'] * 50,
        'date': pd.date_range('2020-01-01', periods=50, freq='W').tolist() * 2,
        'price': np.random.normal(100, 10, n_obs),
        'commodity': ['wheat'] * n_obs
    })
    
    # Create a simple ResultsContainer
    results_dict = {
        'coefficients': {'intercept': 100.0, 'time_trend': 0.1},
        'standard_errors': {'intercept': 5.0, 'time_trend': 0.05},
        'fit_statistics': {
            'r_squared': 0.75,
            'adj_r_squared': 0.74,
            'n_obs': n_obs
        },
        'panel_info': {
            'n_entities': 2,
            'n_time_periods': 50,
            'n_obs': n_obs
        }
    }
    
    results = ResultsContainer(
        commodity="wheat",
        model_type="pooled_panel",
        results=results_dict
    )
    
    # Add residuals directly
    results.residuals = np.random.normal(0, 1, n_obs)
    
    # Test 1: DiagnosticAdapter initialization
    info("\n1. Testing DiagnosticAdapter...")
    try:
        adapter = DiagnosticAdapter(results, tier=1, entity_id_col='market', time_id_col='date')
        info("✓ DiagnosticAdapter created successfully")
    except Exception as e:
        error(f"✗ DiagnosticAdapter failed: {e}")
        return False
    
    # Test 2: DiagnosticReport initialization
    info("\n2. Testing DiagnosticReport...")
    try:
        report = DiagnosticReport(tier=1)
        info("✓ DiagnosticReport created successfully")
    except Exception as e:
        error(f"✗ DiagnosticReport failed: {e}")
        return False
    
    # Test 3: Basic diagnostic test
    info("\n3. Testing basic diagnostic runner...")
    try:
        diagnostics = ThreeTierPanelDiagnostics(tier=1)
        info("✓ ThreeTierPanelDiagnostics created successfully")
        
        # Try to extract residuals via adapter
        residuals = adapter.get_residuals()
        if residuals is not None:
            info(f"✓ Extracted residuals: shape={residuals.shape}")
        else:
            warning("Could not extract residuals")
            
    except Exception as e:
        error(f"✗ Basic diagnostic test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    info("\n" + "="*60)
    info("Basic Diagnostic Test Complete!")
    info("="*60)
    
    return True


if __name__ == "__main__":
    success = test_basic_diagnostics()
    
    if success:
        info("\n✅ Basic diagnostic test passed!")
        sys.exit(0)
    else:
        error("\n❌ Basic diagnostic test failed!")
        sys.exit(1)
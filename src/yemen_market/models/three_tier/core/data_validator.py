"""Data validation utilities for three-tier panel analysis.

This module provides comprehensive validation functions to ensure data
quality and compatibility with econometric requirements.
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime
from typing import List, Tuple, Dict, Optional, Any
from dataclasses import dataclass, field
from yemen_market.utils.logging import info, warning, error, bind

# Set module context
bind(module=__name__)


@dataclass
class ValidationConfig:
    """Configuration for data validation."""

    # Required columns
    required_columns: List[str] = field(default_factory=lambda: ['date', 'market', 'commodity', 'price'])

    # Price validation
    min_price: float = 0.0
    max_price: float = 10000.0

    # Missing data thresholds
    max_missing_pct: float = 0.3
    max_missing_per_series: float = 0.5

    # Panel structure requirements
    min_entities: int = 5
    min_periods: int = 10
    min_obs_per_entity: int = 5

    # Data quality thresholds
    outlier_threshold: float = 5.0  # Z-score threshold
    min_variance: float = 1e-10

    # Schema validation
    schema: Optional[Dict[str, Any]] = None

    # Custom validation rules
    custom_rules: Dict[str, Any] = field(default_factory=dict)


class ValidationResult:
    """Container for validation results."""

    def __init__(self):
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.info: Dict[str, Any] = {}
        self.timestamp = datetime.now()

    @property
    def is_valid(self) -> bool:
        """Check if validation passed (no errors)."""
        return len(self.errors) == 0

    def add_error(self, message: str) -> None:
        """Add an error message."""
        self.errors.append(message)
        error(f"Validation error: {message}")

    def add_warning(self, message: str) -> None:
        """Add a warning message."""
        self.warnings.append(message)
        warning(f"Validation warning: {message}")

    def add_info(self, key: str, value: Any) -> None:
        """Add information to the result."""
        self.info[key] = value

    def summary(self) -> Dict[str, Any]:
        """Get a summary of validation results."""
        return {
            'is_valid': self.is_valid,
            'n_errors': len(self.errors),
            'n_warnings': len(self.warnings),
            'n_info_items': len(self.info),
            'timestamp': self.timestamp.isoformat()
        }

    def generate_report(self) -> str:
        """Generate a human-readable validation report."""
        report = []
        report.append("=" * 50)
        report.append("VALIDATION REPORT")
        report.append("=" * 50)
        report.append(f"Timestamp: {self.timestamp}")
        report.append(f"Valid: {'Yes' if self.is_valid else 'No'}")
        report.append("")

        if self.errors:
            report.append("Errors:")
            for i, error in enumerate(self.errors, 1):
                report.append(f"  {i}. {error}")
            report.append("")

        if self.warnings:
            report.append("Warnings:")
            for i, warning in enumerate(self.warnings, 1):
                report.append(f"  {i}. {warning}")
            report.append("")

        if self.info:
            report.append("Information:")
            for key, value in self.info.items():
                report.append(f"  {key}: {value}")
            report.append("")

        report.append("=" * 50)
        return "\n".join(report)

    def export(self, filepath: str) -> None:
        """Export validation results to JSON file."""
        export_data = {
            'is_valid': self.is_valid,
            'errors': self.errors,
            'warnings': self.warnings,
            'info': self.info,
            'timestamp': self.timestamp.isoformat(),
            'summary': self.summary()
        }

        with open(filepath, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)

    def generate_report(self) -> str:
        """Generate a formatted validation report.

        Returns
        -------
        str
            Formatted validation report
        """
        report = []
        report.append("=" * 50)
        report.append("Validation Report")
        report.append("=" * 50)
        report.append(f"Timestamp: {self.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Valid: {'Yes' if self.is_valid else 'No'}")
        report.append("")

        # Errors section
        report.append("Errors:")
        if self.errors:
            for error in self.errors:
                report.append(f"  - {error}")
        else:
            report.append("  None")
        report.append("")

        # Warnings section
        report.append("Warnings:")
        if self.warnings:
            for warning in self.warnings:
                report.append(f"  - {warning}")
        else:
            report.append("  None")
        report.append("")

        # Information section
        report.append("Information:")
        if self.info:
            for key, value in self.info.items():
                report.append(f"  {key}: {value}")
        else:
            report.append("  None")

        report.append("=" * 50)
        return "\n".join(report)


class PanelDataValidator:
    """Validator for panel data structures and quality checks."""

    def __init__(self):
        """Initialize validator with standard requirements."""
        self.min_obs_per_entity = 10
        self.min_entities = 5
        self.max_missing_pct = 0.5
        self.required_variance = 1e-10

    def validate_panel_structure(self, df: pd.DataFrame,
                               entity_col: str, time_col: str) -> Dict[str, bool]:
        """Validate panel data structure.

        Parameters
        ----------
        df : pd.DataFrame
            Panel data
        entity_col : str
            Entity identifier column
        time_col : str
            Time identifier column

        Returns
        -------
        dict
            Validation results with pass/fail for each check
        """
        results = {}

        # Check if columns exist
        results['columns_exist'] = all(col in df.columns for col in [entity_col, time_col])
        if not results['columns_exist']:
            error(f"Missing required columns: {entity_col} or {time_col}")
            return results

        # Check for duplicates
        duplicates = df.duplicated(subset=[entity_col, time_col])
        results['no_duplicates'] = not duplicates.any()
        if duplicates.any():
            warning(f"Found {duplicates.sum()} duplicate observations")

        # Check entity counts
        entity_counts = df.groupby(entity_col).size()
        results['sufficient_obs_per_entity'] = (entity_counts >= self.min_obs_per_entity).all()
        if not results['sufficient_obs_per_entity']:
            low_count_entities = entity_counts[entity_counts < self.min_obs_per_entity]
            warning(f"{len(low_count_entities)} entities have < {self.min_obs_per_entity} observations")

        # Check total entities
        n_entities = df[entity_col].nunique()
        results['sufficient_entities'] = n_entities >= self.min_entities
        if not results['sufficient_entities']:
            error(f"Only {n_entities} entities (minimum: {self.min_entities})")

        # Check balance
        is_balanced = entity_counts.nunique() == 1
        results['is_balanced'] = is_balanced
        if not is_balanced:
            info(f"Unbalanced panel: observations range from {entity_counts.min()} to {entity_counts.max()}")

        # Overall pass
        results['overall_pass'] = all(v for k, v in results.items() if k != 'is_balanced')

        return results

    def validate_time_series_properties(self, df: pd.DataFrame,
                                      entity_col: str, time_col: str,
                                      value_col: str) -> Dict[str, bool]:
        """Validate time series properties within panel.

        Parameters
        ----------
        df : pd.DataFrame
            Panel data
        entity_col : str
            Entity identifier
        time_col : str
            Time identifier
        value_col : str
            Value column to check

        Returns
        -------
        dict
            Validation results
        """
        results = {}

        # Check time gaps
        time_gaps = []
        for entity in df[entity_col].unique():
            entity_data = df[df[entity_col] == entity].sort_values(time_col)
            if len(entity_data) > 1:
                gaps = entity_data[time_col].diff().dropna()
                if not gaps.empty and gaps.nunique() > 1:
                    time_gaps.append(entity)

        results['regular_time_intervals'] = len(time_gaps) == 0
        if time_gaps:
            warning(f"{len(time_gaps)} entities have irregular time intervals")

        # Check missing values
        missing_pct = df[value_col].isna().sum() / len(df)
        results['acceptable_missing'] = missing_pct <= self.max_missing_pct
        if missing_pct > 0:
            info(f"Missing values: {missing_pct:.1%}")

        # Check variance
        low_variance_entities = []
        for entity in df[entity_col].unique():
            entity_vals = df[df[entity_col] == entity][value_col].dropna()
            if len(entity_vals) > 1 and entity_vals.var() < self.required_variance:
                low_variance_entities.append(entity)

        results['sufficient_variance'] = len(low_variance_entities) == 0
        if low_variance_entities:
            warning(f"{len(low_variance_entities)} entities have near-zero variance")

        # Check for extreme values
        if df[value_col].notna().any():
            z_scores = np.abs((df[value_col] - df[value_col].mean()) / df[value_col].std())
            extreme_count = (z_scores > 5).sum()
            results['no_extreme_outliers'] = extreme_count == 0
            if extreme_count > 0:
                warning(f"Found {extreme_count} extreme outliers (|z| > 5)")
        else:
            results['no_extreme_outliers'] = True

        # Overall pass
        results['overall_pass'] = all(results.values())

        return results

    def validate_for_tier1(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate data for Tier 1 pooled panel analysis.

        Parameters
        ----------
        df : pd.DataFrame
            Input data with market, commodity, date, and price columns

        Returns
        -------
        tuple
            (is_valid, list_of_issues)
        """
        issues = []

        # Check required columns
        required = ['governorate', 'commodity', 'date', 'usd_price']
        missing = set(required) - set(df.columns)
        if missing:
            # Allow 'market' as alias for 'governorate'
            if 'market' in df.columns and 'governorate' in missing:
                df = df.copy()
                df['governorate'] = df['market']
                missing.remove('governorate')
            if missing:
                issues.append(f"Missing required columns: {missing}")
                return False, issues

        # Handle entity column - use existing if present, create if not
        df_copy = df.copy()
        if 'entity' not in df_copy.columns:
            # Create entity column from governorate and commodity
            df_copy['entity'] = df_copy['governorate'] + "_" + df_copy['commodity']
        else:
            # Use existing entity column
            info("Using existing entity column for validation")

        # Validate panel structure
        struct_results = self.validate_panel_structure(df_copy, 'entity', 'date')
        if not struct_results['overall_pass']:
            issues.extend([k for k, v in struct_results.items() if not v and k != 'overall_pass'])

        # Validate time series properties
        ts_results = self.validate_time_series_properties(df_copy, 'entity', 'date', 'usd_price')
        if not ts_results['overall_pass']:
            issues.extend([k for k, v in ts_results.items() if not v and k != 'overall_pass'])

        # Check minimum panel dimensions
        n_entities = df_copy['entity'].nunique()
        n_periods = df_copy['date'].nunique()

        if n_entities < 10:
            issues.append(f"Too few entities: {n_entities} (minimum: 10)")
        if n_periods < 20:
            issues.append(f"Too few time periods: {n_periods} (minimum: 20)")

        is_valid = len(issues) == 0
        if is_valid:
            info("Data validated successfully for Tier 1 analysis")
        else:
            error(f"Data validation failed with {len(issues)} issues")

        return is_valid, issues

    def validate_for_tier2(self, df: pd.DataFrame, commodity: str) -> Tuple[bool, List[str]]:
        """Validate data for Tier 2 commodity-specific analysis.

        Parameters
        ----------
        df : pd.DataFrame
            Input data
        commodity : str
            Commodity to analyze

        Returns
        -------
        tuple
            (is_valid, list_of_issues)
        """
        issues = []

        # Filter for commodity
        commodity_df = df[df['commodity'] == commodity]

        if commodity_df.empty:
            issues.append(f"No data for commodity: {commodity}")
            return False, issues

        # Validate panel structure
        struct_results = self.validate_panel_structure(commodity_df, 'governorate', 'date')
        if not struct_results['overall_pass']:
            issues.extend([k for k, v in struct_results.items() if not v and k != 'overall_pass'])

        # Check minimum requirements for VECM
        n_markets = commodity_df['governorate'].nunique()
        n_periods = commodity_df['date'].nunique()

        if n_markets < 3:
            issues.append(f"Too few markets for {commodity}: {n_markets} (minimum: 3)")
        if n_periods < 50:
            issues.append(f"Too few periods for {commodity}: {n_periods} (minimum: 50)")

        # Check for sufficient price variation
        price_cv = commodity_df.groupby('governorate')['usd_price'].apply(
            lambda x: x.std() / x.mean() if x.mean() > 0 else 0
        )
        low_variation_markets = price_cv[price_cv < 0.05]
        if len(low_variation_markets) > 0:
            issues.append(f"{len(low_variation_markets)} markets have CV < 5% for {commodity}")

        is_valid = len(issues) == 0
        if is_valid:
            info(f"Data validated successfully for Tier 2 analysis of {commodity}")
        else:
            error(f"Data validation failed for {commodity} with {len(issues)} issues")

        return is_valid, issues

    def validate_for_tier3(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate data for Tier 3 factor analysis.

        Parameters
        ----------
        df : pd.DataFrame
            Input data in wide format (time × series)

        Returns
        -------
        tuple
            (is_valid, list_of_issues)
        """
        issues = []

        # Check minimum dimensions
        n_periods, n_series = df.shape

        if n_periods < 50:
            issues.append(f"Too few time periods: {n_periods} (minimum: 50)")
        if n_series < 10:
            issues.append(f"Too few series: {n_series} (minimum: 10)")

        # Check missing data
        missing_pct = df.isna().sum().sum() / (n_periods * n_series)
        if missing_pct > 0.3:
            issues.append(f"Too many missing values: {missing_pct:.1%} (maximum: 30%)")

        # Check for constant series
        constant_series = []
        for col in df.columns:
            if df[col].nunique() <= 1:
                constant_series.append(col)

        if constant_series:
            issues.append(f"{len(constant_series)} series have no variation")

        # Check correlation structure
        if n_series >= 2 and not df.empty:
            # Get pairwise correlations for non-missing data
            corr_matrix = df.corr()
            # Check if there's some correlation structure
            off_diagonal = corr_matrix.values[~np.eye(corr_matrix.shape[0], dtype=bool)]
            if np.nanmean(np.abs(off_diagonal)) < 0.1:
                warning("Low average correlation between series - factor analysis may not be appropriate")

        is_valid = len(issues) == 0
        if is_valid:
            info("Data validated successfully for Tier 3 factor analysis")
        else:
            error(f"Data validation failed with {len(issues)} issues")

        return is_valid, issues

    def check_data_quality(self, df: pd.DataFrame) -> pd.DataFrame:
        """Generate comprehensive data quality report.

        Parameters
        ----------
        df : pd.DataFrame
            Input data

        Returns
        -------
        pd.DataFrame
            Quality metrics by variable
        """
        quality_report = []

        for col in df.columns:
            col_data = df[col]

            # Skip non-numeric columns for some metrics
            if pd.api.types.is_numeric_dtype(col_data):
                metrics = {
                    'variable': col,
                    'dtype': str(col_data.dtype),
                    'n_obs': len(col_data),
                    'n_missing': col_data.isna().sum(),
                    'pct_missing': col_data.isna().sum() / len(col_data) * 100,
                    'n_unique': col_data.nunique(),
                    'mean': col_data.mean() if not col_data.empty else np.nan,
                    'std': col_data.std() if not col_data.empty else np.nan,
                    'min': col_data.min() if not col_data.empty else np.nan,
                    'max': col_data.max() if not col_data.empty else np.nan,
                    'n_zeros': (col_data == 0).sum(),
                    'n_negative': (col_data < 0).sum() if pd.api.types.is_numeric_dtype(col_data) else 0
                }
            else:
                metrics = {
                    'variable': col,
                    'dtype': str(col_data.dtype),
                    'n_obs': len(col_data),
                    'n_missing': col_data.isna().sum(),
                    'pct_missing': col_data.isna().sum() / len(col_data) * 100,
                    'n_unique': col_data.nunique(),
                    'mean': np.nan,
                    'std': np.nan,
                    'min': np.nan,
                    'max': np.nan,
                    'n_zeros': np.nan,
                    'n_negative': np.nan
                }

            quality_report.append(metrics)

        return pd.DataFrame(quality_report)


class DataValidator:
    """Main data validator class for three-tier analysis."""

    def __init__(self, config: Optional[ValidationConfig] = None):
        """Initialize validator with configuration.

        Parameters
        ----------
        config : ValidationConfig, optional
            Validation configuration. If None, uses default config.
        """
        self.config = config or ValidationConfig()
        bind(class_name=self.__class__.__name__)

    def validate_columns(self, data: pd.DataFrame) -> ValidationResult:
        """Validate required columns are present.

        Parameters
        ----------
        data : pd.DataFrame
            Input data to validate

        Returns
        -------
        ValidationResult
            Validation results
        """
        result = ValidationResult()

        missing_cols = set(self.config.required_columns) - set(data.columns)
        if missing_cols:
            for col in missing_cols:
                result.add_error(f"Missing required column: {col}")
        else:
            result.add_warning("All required columns present")

        result.add_info('n_columns', len(data.columns))
        result.add_info('columns', list(data.columns))

        return result

    def validate_data_types(self, data: pd.DataFrame) -> ValidationResult:
        """Validate data types are appropriate.

        Parameters
        ----------
        data : pd.DataFrame
            Input data to validate

        Returns
        -------
        ValidationResult
            Validation results
        """
        result = ValidationResult()

        # Check date column
        if 'date' in data.columns:
            if not pd.api.types.is_datetime64_any_dtype(data['date']):
                result.add_error("Date column is not datetime type")

        # Check numeric columns
        numeric_cols = ['price', 'volume']
        for col in numeric_cols:
            if col in data.columns:
                if not pd.api.types.is_numeric_dtype(data[col]):
                    result.add_error(f"Column '{col}' should be numeric but is {data[col].dtype}")

        # Check string columns
        string_cols = ['market', 'commodity']
        for col in string_cols:
            if col in data.columns:
                if not pd.api.types.is_object_dtype(data[col]) and not pd.api.types.is_string_dtype(data[col]):
                    result.add_warning(f"Column '{col}' should be string type but is {data[col].dtype}")

        return result

    def validate_date_format(self, data: pd.DataFrame) -> ValidationResult:
        """Validate date format and consistency.

        Parameters
        ----------
        data : pd.DataFrame
            Input data to validate

        Returns
        -------
        ValidationResult
            Validation results
        """
        result = ValidationResult()

        if 'date' not in data.columns:
            result.add_error("No date column found")
            return result

        # Try to convert to datetime if not already
        try:
            if not pd.api.types.is_datetime64_any_dtype(data['date']):
                # Use explicit format inference to avoid warnings
                import warnings
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore", UserWarning)
                    warnings.simplefilter("ignore", FutureWarning)
                    # Try common date formats first to avoid inference warnings
                    try:
                        pd.to_datetime(data['date'], format='%Y-%m-%d', errors='raise')
                    except (ValueError, TypeError):
                        try:
                            pd.to_datetime(data['date'], format='%Y-%m-%d %H:%M:%S', errors='raise')
                        except (ValueError, TypeError):
                            # Fall back to automatic inference with warnings suppressed
                            pd.to_datetime(data['date'], errors='raise')
            result.add_info('date_format', 'valid')
        except Exception as e:
            result.add_error(f"Invalid date format: {str(e)}")

        # Check for missing dates
        if data['date'].isna().any():
            result.add_warning(f"{data['date'].isna().sum()} missing dates found")

        return result

    def validate_price_ranges(self, data: pd.DataFrame) -> ValidationResult:
        """Validate price ranges and detect outliers.

        Parameters
        ----------
        data : pd.DataFrame
            Input data to validate

        Returns
        -------
        ValidationResult
            Validation results
        """
        result = ValidationResult()

        if 'price' not in data.columns:
            result.add_warning("No price column found for validation")
            return result

        prices = data['price'].dropna()

        # Check for negative prices
        negative_prices = (prices < 0).sum()
        if negative_prices > 0:
            result.add_error(f"Negative price values found: {negative_prices}")

        # Check for extreme outliers
        if len(prices) > 0:
            z_scores = np.abs((prices - prices.mean()) / prices.std())
            outliers = (z_scores > self.config.outlier_threshold).sum()
            if outliers > 0:
                result.add_warning(f"Potential outlier prices detected: {outliers}")

        # Check price range
        if len(prices) > 0:
            min_price, max_price = prices.min(), prices.max()
            if min_price < self.config.min_price or max_price > self.config.max_price:
                result.add_warning(f"Prices outside expected range [{self.config.min_price}, {self.config.max_price}]: [{min_price:.2f}, {max_price:.2f}]")

        result.add_info('price_min', prices.min() if len(prices) > 0 else None)
        result.add_info('price_max', prices.max() if len(prices) > 0 else None)
        result.add_info('price_mean', prices.mean() if len(prices) > 0 else None)

        return result

    def validate_panel_structure(self, data: pd.DataFrame) -> ValidationResult:
        """Validate panel data structure.

        Parameters
        ----------
        data : pd.DataFrame
            Input data to validate

        Returns
        -------
        ValidationResult
            Validation results
        """
        result = ValidationResult()

        required_cols = ['date', 'market', 'commodity']
        missing_cols = [col for col in required_cols if col not in data.columns]
        if missing_cols:
            result.add_error(f"Missing columns for panel structure: {missing_cols}")
            return result

        # Check for duplicates
        duplicates = data.duplicated(subset=['date', 'market', 'commodity'])
        if duplicates.any():
            result.add_error(f"Duplicate observations found: {duplicates.sum()}")

        # Panel dimensions
        n_markets = data['market'].nunique()
        n_commodities = data['commodity'].nunique()
        n_periods = data['date'].nunique()

        result.add_info('n_markets', n_markets)
        result.add_info('n_commodities', n_commodities)
        result.add_info('n_periods', n_periods)

        # Check balance
        expected_obs = n_markets * n_commodities * n_periods
        actual_obs = len(data)
        is_balanced = expected_obs == actual_obs
        result.add_info('is_balanced', is_balanced)

        if not is_balanced:
            result.add_warning(f"Unbalanced panel: {actual_obs}/{expected_obs} observations")

        # Check minimum requirements
        if n_markets < self.config.min_entities:
            result.add_error(f"Too few markets: {n_markets} (minimum: {self.config.min_entities})")
        if n_periods < self.config.min_periods:
            result.add_error(f"Too few periods: {n_periods} (minimum: {self.config.min_periods})")

        return result

    def validate_missing_data(self, data: pd.DataFrame) -> ValidationResult:
        """Validate missing data patterns.

        Parameters
        ----------
        data : pd.DataFrame
            Input data to validate

        Returns
        -------
        ValidationResult
            Validation results
        """
        result = ValidationResult()

        # Overall missing data
        total_missing = data.isna().sum().sum()
        total_cells = data.shape[0] * data.shape[1]
        missing_pct = total_missing / total_cells

        result.add_info('missing_pct', missing_pct)
        result.add_info('missing_count', total_missing)

        if missing_pct > self.config.max_missing_pct:
            result.add_error(f"Excessive missing data: {missing_pct:.1%} (maximum: {self.config.max_missing_pct:.1%})")
        elif missing_pct > 0:
            result.add_warning(f"Missing values detected: {missing_pct:.1%}")

        # Missing by column
        col_missing = data.isna().sum()
        for col, missing_count in col_missing.items():
            if missing_count > 0:
                col_missing_pct = missing_count / len(data)
                if col_missing_pct > self.config.max_missing_per_series:
                    result.add_warning(f"High missing data in {col}: {col_missing_pct:.1%}")

        return result

    def validate_market_consistency(self, data: pd.DataFrame) -> ValidationResult:
        """Validate market name consistency.

        Parameters
        ----------
        data : pd.DataFrame
            Input data to validate

        Returns
        -------
        ValidationResult
            Validation results
        """
        result = ValidationResult()

        if 'market' not in data.columns:
            result.add_warning("No market column found")
            return result

        markets = data['market'].unique()

        # Check for potential duplicates (case, whitespace)
        normalized_markets = [str(m).strip().lower() for m in markets if pd.notna(m)]
        if len(set(normalized_markets)) < len([m for m in markets if pd.notna(m)]):
            result.add_warning("Potential duplicate markets detected (case/whitespace differences)")

        result.add_info('n_markets', len(markets))
        result.add_info('markets', list(markets))

        return result

    def validate_time_continuity(self, data: pd.DataFrame) -> ValidationResult:
        """Validate time series continuity.

        Parameters
        ----------
        data : pd.DataFrame
            Input data to validate

        Returns
        -------
        ValidationResult
            Validation results
        """
        result = ValidationResult()

        if 'date' not in data.columns:
            result.add_error("No date column found")
            return result

        # Check for time gaps
        dates = pd.to_datetime(data['date']).sort_values().unique()
        if len(dates) > 1:
            date_diffs = pd.Series(dates).diff().dropna()

            # Check if intervals are regular
            if date_diffs.nunique() > 1:
                result.add_warning("Irregular time intervals detected")

            # Check for large gaps
            median_diff = date_diffs.median()
            large_gaps = date_diffs > median_diff * 2
            if large_gaps.any():
                result.add_warning(f"Large time gaps detected: {large_gaps.sum()}")
                # Add specific gap information
                gap_indices = np.where(large_gaps)[0]
                for idx in gap_indices:
                    gap_start = dates[idx]
                    gap_end = dates[idx + 1] if idx + 1 < len(dates) else dates[idx]
                    result.add_warning(f"Time gap from {gap_start} to {gap_end}")

        result.add_info('date_range', f"{dates.min()} to {dates.max()}")
        result.add_info('n_periods', len(dates))

        return result

    def validate_relationships(self, data: pd.DataFrame) -> ValidationResult:
        """Validate logical relationships between variables.

        Parameters
        ----------
        data : pd.DataFrame
            Input data to validate

        Returns
        -------
        ValidationResult
            Validation results
        """
        result = ValidationResult()

        # Check price * volume = total_value if present
        if all(col in data.columns for col in ['price', 'volume', 'total_value']):
            calculated_value = data['price'] * data['volume']
            diff = np.abs(calculated_value - data['total_value'])
            tolerance = 0.01  # 1% tolerance

            inconsistent = diff > (data['total_value'] * tolerance)
            if inconsistent.any():
                result.add_warning(f"Inconsistent price*volume calculations: {inconsistent.sum()}")

        return result

    def validate_all(self, data: pd.DataFrame) -> ValidationResult:
        """Run all validation checks.

        Parameters
        ----------
        data : pd.DataFrame
            Input data to validate

        Returns
        -------
        ValidationResult
            Comprehensive validation results
        """
        result = ValidationResult()

        # Run all validation methods
        validation_methods = [
            self.validate_columns,
            self.validate_data_types,
            self.validate_date_format,
            self.validate_price_ranges,
            self.validate_panel_structure,
            self.validate_missing_data,
            self.validate_market_consistency,
            self.validate_time_continuity,
            self.validate_relationships
        ]

        checks_passed = 0
        checks_failed = 0

        for method in validation_methods:
            try:
                method_result = method(data)

                # Merge results
                result.errors.extend(method_result.errors)
                result.warnings.extend(method_result.warnings)
                result.info.update(method_result.info)

                if method_result.is_valid:
                    checks_passed += 1
                else:
                    checks_failed += 1

            except Exception as e:
                result.add_error(f"Validation method {method.__name__} failed: {str(e)}")
                checks_failed += 1

        result.add_info('validation_timestamp', datetime.now().isoformat())
        result.add_info('n_checks_passed', checks_passed)
        result.add_info('n_checks_failed', checks_failed)
        result.add_info('total_checks', len(validation_methods))

        return result

    def fix_common_issues(self, data: pd.DataFrame) -> pd.DataFrame:
        """Automatically fix common data issues.

        Parameters
        ----------
        data : pd.DataFrame
            Input data with potential issues

        Returns
        -------
        pd.DataFrame
            Data with common issues fixed
        """
        fixed_data = data.copy()

        # Fix market names (strip whitespace, standardize case)
        if 'market' in fixed_data.columns:
            fixed_data['market'] = fixed_data['market'].astype(str).str.strip()
            # Standardize common variations
            market_fixes = {
                'ADEN': 'Aden',
                'aden': 'Aden',
                'SANA\'A': 'Sana\'a',
                'sana\'a': 'Sana\'a',
                'TAIZ': 'Taiz',
                'taiz': 'Taiz'
            }
            fixed_data['market'] = fixed_data['market'].replace(market_fixes)

        # Fix negative prices (replace with NaN)
        if 'price' in fixed_data.columns:
            negative_mask = fixed_data['price'] < 0
            if negative_mask.any():
                info(f"Replacing {negative_mask.sum()} negative prices with NaN")
                fixed_data.loc[negative_mask, 'price'] = np.nan

        # Convert date column to datetime
        if 'date' in fixed_data.columns:
            if not pd.api.types.is_datetime64_any_dtype(fixed_data['date']):
                try:
                    fixed_data['date'] = pd.to_datetime(fixed_data['date'])
                except Exception as e:
                    warning(f"Could not convert date column: {e}")

        return fixed_data

    def validate_batch(self, datasets: Dict[str, pd.DataFrame]) -> Dict[str, ValidationResult]:
        """Validate multiple datasets.

        Parameters
        ----------
        datasets : dict
            Dictionary of dataset name -> DataFrame

        Returns
        -------
        dict
            Dictionary of dataset name -> ValidationResult
        """
        results = {}

        for name, data in datasets.items():
            info(f"Validating dataset: {name}")
            results[name] = self.validate_all(data)

        return results

    def validate_against_schema(self, data: pd.DataFrame) -> ValidationResult:
        """Validate data against a predefined schema.

        Parameters
        ----------
        data : pd.DataFrame
            Input data to validate

        Returns
        -------
        ValidationResult
            Schema validation results
        """
        result = ValidationResult()

        if not self.config.schema:
            result.add_warning("No schema defined for validation")
            return result

        for col_name, col_schema in self.config.schema.items():
            if col_schema.get('required', False) and col_name not in data.columns:
                result.add_error(f"Required column '{col_name}' missing")
                continue

            if col_name not in data.columns:
                continue

            col_data = data[col_name]

            # Check data type
            expected_type = col_schema.get('type')
            if expected_type == 'datetime' and not pd.api.types.is_datetime64_any_dtype(col_data):
                result.add_error(f"Column '{col_name}' should be datetime")
            elif expected_type == 'numeric' and not pd.api.types.is_numeric_dtype(col_data):
                result.add_error(f"Column '{col_name}' should be numeric")
            elif expected_type == 'string' and not (pd.api.types.is_object_dtype(col_data) or pd.api.types.is_string_dtype(col_data)):
                result.add_error(f"Column '{col_name}' should be string")

            # Check value constraints
            if 'values' in col_schema:
                allowed_values = col_schema['values']
                invalid_values = col_data[~col_data.isin(allowed_values)].dropna()
                if len(invalid_values) > 0:
                    result.add_error(f"Column '{col_name}' has values not in allowed values: {invalid_values.unique()}")

            # Check numeric constraints
            if expected_type == 'numeric':
                if 'min' in col_schema:
                    min_violations = (col_data < col_schema['min']).sum()
                    if min_violations > 0:
                        result.add_error(f"Column '{col_name}' has {min_violations} values below minimum {col_schema['min']}")

                if 'max' in col_schema:
                    max_violations = (col_data > col_schema['max']).sum()
                    if max_violations > 0:
                        result.add_error(f"Column '{col_name}' has {max_violations} values above maximum {col_schema['max']}")

        return result
"""Panel dataset builder for Yemen market integration analysis.

This module integrates all data sources (WFP prices, exchange rates, ACAPS control
zones, spatial mappings) into analysis-ready panel datasets for econometric modeling.
"""

from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd

from ..config.settings import PROCESSED_DATA_DIR, ANALYSIS_CONFIG
from ..utils.logging import (
    bind, context, timer, progress, log_data_shape,
    info, warning, error, debug
)


class PanelBuilder:
    """Build integrated panel datasets for econometric analysis.
    
    This class handles:
    - Merging WFP price data with control zone mappings
    - Creating balanced panel structures
    - Calculating exchange rate differentials between zones
    - Adding temporal features and lags
    - Handling missing data appropriately
    - Creating model-specific datasets
    
    Attributes:
        data_dir: Directory containing processed data
        output_dir: Directory for saving panel datasets
        commodities: List of commodities to include
        frequency: Panel frequency ('M' for monthly, 'W' for weekly)
    """
    
    def __init__(self,
                 data_dir: Optional[Path] = None,
                 output_dir: Optional[Path] = None,
                 commodities: Optional[List[str]] = None,
                 frequency: str = 'M'):
        """Initialize panel builder.
        
        Args:
            data_dir: Directory with processed data components
            output_dir: Output directory for panel datasets
            commodities: Commodities to include (None for all)
            frequency: Panel frequency ('M' or 'W')
        """
        self.data_dir = data_dir or PROCESSED_DATA_DIR
        self.output_dir = output_dir or PROCESSED_DATA_DIR / "panels"
        if commodities is None:
            # Get from ANALYSIS_CONFIG and convert to proper case
            config_commodities = ANALYSIS_CONFIG.get(
                'commodities', 
                ['Wheat', 'Wheat Flour', 'Rice (Imported)', 'Sugar', 'Oil (Vegetable)']
            )
            # Convert to match WFP data format
            self.commodities = []
            for c in config_commodities:
                # Handle special cases
                if c.lower() == "wheat flour":
                    self.commodities.append("Wheat Flour")
                elif c.lower() == "rice (imported)":
                    self.commodities.append("Rice (Imported)")
                elif c.lower() == "beans (kidney red)":
                    self.commodities.append("Beans (Kidney Red)")
                elif c.lower() == "beans (white)":
                    self.commodities.append("Beans (White)")
                elif c.lower() == "peas (yellow, split)":
                    self.commodities.append("Peas (Yellow, Split)")
                elif c.lower() == "meat (chicken)":
                    self.commodities.append("Meat (Chicken)")
                elif c.lower() == "meat (mutton)":
                    self.commodities.append("Meat (Mutton)")
                elif c.lower() == "oil (vegetable)":
                    self.commodities.append("Oil (Vegetable)")
                elif c.lower() == "oil (sunflower)":
                    self.commodities.append("Oil (Sunflower)")
                elif c.lower() == "fuel (diesel)":
                    self.commodities.append("Fuel (Diesel)")
                elif c.lower() == "fuel (petrol-gasoline)":
                    self.commodities.append("Fuel (Petrol-Gasoline)")
                elif c.lower() == "fuel (gas)":
                    self.commodities.append("Fuel (Gas)")
                else:
                    # Simple title case for others
                    self.commodities.append(c.title())
        else:
            self.commodities = commodities
        self.frequency = frequency
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        info("PanelBuilder initialized", 
             commodities=len(self.commodities),
             frequency=frequency)
    
    def load_component_data(self) -> Dict[str, pd.DataFrame]:
        """Load all component datasets.
        
        Returns:
            Dictionary of loaded datasets
        """
        with timer("load_component_data"):
            info("Loading component datasets")
        
        data = {}
        
        # Load WFP commodity price data
        commodity_price_path = self.data_dir / "wfp_commodity_prices.parquet"
        if commodity_price_path.exists():
            data['prices'] = pd.read_parquet(commodity_price_path)
            log_data_shape("commodity_price_data", data['prices'])
        else:
            # Fallback to panel data if commodity prices not available
            panel_path = self.data_dir / "wfp_market_panel.parquet"
            if panel_path.exists():
                data['prices'] = pd.read_parquet(panel_path)
                warning("Using aggregated panel data, commodity-level prices not available")
                log_data_shape("panel_data", data['prices'])
            else:
                raise FileNotFoundError(f"No price data found at {commodity_price_path} or {panel_path}")
        
        # Load exchange rate data - check multiple locations
        exchange_paths = [
            self.data_dir.parent / "interim" / "exchange_rates.parquet",  # Current location
            self.data_dir / "wfp" / "exchange_rates.parquet",  # Legacy location
        ]
        
        for exchange_path in exchange_paths:
            if exchange_path.exists():
                data['exchange_rates'] = pd.read_parquet(exchange_path)
                log_data_shape("exchange_rate_data", data['exchange_rates'])
                break
        
        # Load market-zone mappings
        spatial_path = self.data_dir / "spatial" / "market_zones_temporal.parquet"
        if spatial_path.exists():
            data['market_zones'] = pd.read_parquet(spatial_path)
            log_data_shape("market_zone_data", data['market_zones'])
        else:
            # Try current mapping if temporal not available
            current_path = self.data_dir / "spatial" / "market_zones_current.parquet"
            if current_path.exists():
                data['market_zones'] = pd.read_parquet(current_path)
                info("Using current market-zone mapping (no temporal data)")
        
        # Load control zone time series
        control_path = self.data_dir / "control_zones" / "control_zones_monthly.parquet"
        if control_path.exists():
            data['control_zones'] = pd.read_parquet(control_path)
            log_data_shape("control_zone_data", data['control_zones'])
        
        # Load conflict data
        conflict_path = self.data_dir / "conflict" / "conflict_metrics.parquet"
        if conflict_path.exists():
            data['conflict'] = pd.read_parquet(conflict_path)
            log_data_shape("conflict_data", data['conflict'])
            info("Loaded conflict metrics", 
                 markets=data['conflict']['market_id'].nunique(),
                 time_periods=data['conflict']['year_month'].nunique())
        else:
            warning("No conflict data found - threshold models may be limited")
        
        return data
    
    def create_price_panel(self, data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """Create commodity price panel dataset.
        
        Args:
            data: Dictionary of component datasets
            
        Returns:
            Panel DataFrame with prices and controls
        """
        with timer("create_price_panel"):
            info("Creating price panel dataset")
        
        # Start with price data
        prices = data['prices'].copy()
        
        # Filter commodities
        if self.commodities:
            prices = prices[prices['commodity'].isin(self.commodities)]
            info("Filtered commodities", records=len(prices), commodities=self.commodities)
        
        # Ensure date column
        if 'date' in prices.columns:
            prices['date'] = pd.to_datetime(prices['date'])
        
        # Add time dimensions
        prices['year'] = prices['date'].dt.year
        prices['month'] = prices['date'].dt.month
        prices['year_month'] = prices['date'].dt.to_period('M')
        
        # Merge with market zones
        if 'market_zones' in data:
            zones = data['market_zones'].copy()
            
            # Handle temporal vs static mapping
            if 'date' in zones.columns:
                # Temporal mapping - merge on market and date
                zones['year_month'] = pd.to_datetime(zones['date']).dt.to_period('M')
                
                panel = prices.merge(
                    zones[['market_id', 'year_month', 'control_zone', 
                          'market_governorate', 'market_district']],
                    left_on=['market_id', 'year_month'],
                    right_on=['market_id', 'year_month'],
                    how='left'
                )
            else:
                # Static mapping - merge on market only
                panel = prices.merge(
                    zones[['market_id', 'control_zone', 
                          'market_governorate', 'market_district']],
                    on='market_id',
                    how='left'
                )
            
            match_rate = panel['control_zone'].notna().sum() / len(panel) * 100
            info("Merged with control zones", 
                 matched=panel['control_zone'].notna().sum(),
                 total=len(panel),
                 match_rate=f"{match_rate:.1f}%")
        else:
            panel = prices
            warning("No market-zone mapping available")
        
        # Merge with conflict data
        if 'conflict' in data:
            conflict = data['conflict'].copy()
            
            # Convert year_month to period if it's a string
            if 'year_month' in conflict.columns and conflict['year_month'].dtype == 'object':
                conflict['year_month'] = pd.to_datetime(conflict['year_month']).dt.to_period('M')
            
            # Select key conflict variables for threshold models
            conflict_vars = [
                'market_id', 'year_month',
                'conflict_intensity', 'total_fatalities', 'n_battles',
                'n_explosions', 'n_violence_civilians',
                'conflict_intensity_lag1', 'conflict_intensity_lag2', 
                'conflict_intensity_lag3', 'conflict_ma3'
            ]
            
            # Only keep columns that exist
            conflict_vars = [col for col in conflict_vars if col in conflict.columns]
            conflict = conflict[conflict_vars]
            
            # Merge on market_id and year_month
            panel = panel.merge(
                conflict,
                on=['market_id', 'year_month'],
                how='left'
            )
            
            conflict_match_rate = panel['conflict_intensity'].notna().sum() / len(panel) * 100
            info("Merged with conflict data",
                 matched=panel['conflict_intensity'].notna().sum(),
                 total=len(panel),
                 match_rate=f"{conflict_match_rate:.1f}%")
        else:
            warning("No conflict data available for integration")
        
        # Sort panel
        panel = panel.sort_values(['commodity', 'market_id', 'date'])
        
        return panel
    
    def create_exchange_rate_panel(self, data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """Create exchange rate panel with zone differentials.
        
        Args:
            data: Dictionary of component datasets
            
        Returns:
            Exchange rate panel DataFrame
        """
        with timer("create_exchange_rate_panel"):
            info("Creating exchange rate panel with dual exchange rate indicators")
        
        if 'exchange_rates' not in data:
            warning("No exchange rate data available - attempting to derive from price data")
            return self._derive_exchange_rates_from_prices(data)
        
        rates = data['exchange_rates'].copy()
        
        # Handle different date formats
        if 'date' in rates.columns:
            rates['date'] = pd.to_datetime(rates['date'])
            rates['year_month'] = rates['date'].dt.to_period('M')
        elif 'year_month' in rates.columns and 'date' not in rates.columns:
            # If only year_month exists, keep it as is
            pass
        else:
            raise ValueError("Exchange rates must have either 'date' or 'year_month' column")
        
        # Create market_id if not present
        if 'market_id' not in rates.columns and 'governorate' in rates.columns and 'market_name' in rates.columns:
            rates['market_id'] = (rates['governorate'] + '_' + rates['market_name']).str.replace(' ', '_')
        
        # Merge with market zones
        if 'market_zones' in data:
            zones = data['market_zones'].copy()
            
            if 'date' in zones.columns:
                zones['year_month'] = pd.to_datetime(zones['date']).dt.to_period('M')
                exchange_panel = rates.merge(
                    zones[['market_id', 'year_month', 'control_zone']],
                    on=['market_id', 'year_month'],
                    how='left'
                )
            else:
                exchange_panel = rates.merge(
                    zones[['market_id', 'control_zone']],
                    on='market_id',
                    how='left'
                )
        else:
            exchange_panel = rates
        
        # Calculate zone-level exchange rates
        if 'control_zone' in exchange_panel.columns:
            zone_rates = exchange_panel.groupby(
                ['year_month', 'control_zone']
            )['exchange_rate'].agg(['mean', 'std', 'count']).reset_index()
            
            zone_rates.columns = ['year_month', 'control_zone', 
                                 'zone_exchange_rate', 'zone_rate_std', 'n_markets']
            
            # Calculate differentials
            info("Calculating exchange rate differentials between zones")
            
            # Get rates for main zones
            houthi_rates = zone_rates[zone_rates['control_zone'].str.lower() == 'houthi'][
                ['year_month', 'zone_exchange_rate']
            ].rename(columns={'zone_exchange_rate': 'houthi_rate'})
            
            gov_rates = zone_rates[zone_rates['control_zone'].str.lower() == 'government'][
                ['year_month', 'zone_exchange_rate']
            ].rename(columns={'zone_exchange_rate': 'gov_rate'})
            
            # Merge to calculate differentials
            differentials = houthi_rates.merge(gov_rates, on='year_month', how='outer')
            differentials['rate_differential'] = (
                differentials['houthi_rate'] - differentials['gov_rate']
            )
            differentials['rate_differential_pct'] = (
                differentials['rate_differential'] / differentials['gov_rate'] * 100
            )
            
            # Add back to panel
            exchange_panel = exchange_panel.merge(
                zone_rates[['year_month', 'control_zone', 'zone_exchange_rate']],
                on=['year_month', 'control_zone'],
                how='left'
            )
            
            exchange_panel = exchange_panel.merge(
                differentials[['year_month', 'rate_differential', 'rate_differential_pct']],
                on='year_month',
                how='left'
            )
        
        return exchange_panel
    
    def add_temporal_features(self, panel: pd.DataFrame) -> pd.DataFrame:
        """Add temporal features for time series analysis.
        
        Args:
            panel: Input panel DataFrame
            
        Returns:
            Panel with additional temporal features
        """
        with timer("add_temporal_features"):
            info("Adding temporal features")
        
        # Ensure sorted by entity and time
        if 'commodity' in panel.columns:
            panel = panel.sort_values(['commodity', 'market_id', 'date'])
            group_cols = ['commodity', 'market_id']
        else:
            panel = panel.sort_values(['market_id', 'date'])
            group_cols = ['market_id']
        
        # Add lags for key variables
        lag_vars = []
        if 'price_usd' in panel.columns:
            lag_vars.append('price_usd')
        if 'parallel_rate' in panel.columns:
            lag_vars.append('parallel_rate')
        if 'rate_differential' in panel.columns:
            lag_vars.append('rate_differential')
        
        for var in lag_vars:
            for lag in [1, 2, 3]:
                panel[f'{var}_lag{lag}'] = panel.groupby(group_cols)[var].shift(lag)
                
            # Add differences
            panel[f'{var}_diff'] = panel.groupby(group_cols)[var].diff()
            panel[f'{var}_pct_change'] = panel.groupby(group_cols)[var].pct_change()
        
        # Add rolling statistics
        for var in lag_vars:
            # 3-month rolling average
            panel[f'{var}_ma3'] = panel.groupby(group_cols)[var].transform(
                lambda x: x.rolling(window=3, min_periods=1).mean()
            )
            
            # 3-month rolling std
            panel[f'{var}_std3'] = panel.groupby(group_cols)[var].transform(
                lambda x: x.rolling(window=3, min_periods=2).std()
            )
        
        # Add time trend
        panel['time_trend'] = panel.groupby(group_cols).cumcount() + 1
        
        # Add seasonal indicators
        panel['quarter'] = panel['date'].dt.quarter
        panel['is_ramadan'] = panel['month'].isin([3, 4, 5])  # Approximate
        
        # Add conflict intensity proxy (if control changes)
        if 'control_zone' in panel.columns:
            panel['is_contested'] = panel['control_zone'] == 'contested'
            
            # Count control changes in last 3 months
            if 'zone_changed' in panel.columns:
                panel['recent_instability'] = panel.groupby(group_cols)['zone_changed'].transform(
                    lambda x: x.rolling(window=3, min_periods=1).sum()
                )
        
        # Add conflict regime indicators based on methodology
        if 'conflict_intensity' in panel.columns:
            # Define conflict regimes (low, medium, high) using quantiles
            q33 = panel['conflict_intensity'].quantile(0.33)
            q67 = panel['conflict_intensity'].quantile(0.67)
            
            panel['conflict_regime'] = pd.cut(
                panel['conflict_intensity'],
                bins=[-np.inf, q33, q67, np.inf],
                labels=['low', 'medium', 'high']
            )
            
            # Binary indicators for regime switching models
            panel['high_conflict'] = (panel['conflict_intensity'] > q67).astype(int)
            panel['low_conflict'] = (panel['conflict_intensity'] <= q33).astype(int)
            
            info("Added conflict regime indicators",
                 low_threshold=f"{q33:.1f}",
                 high_threshold=f"{q67:.1f}")
        
        temporal_cols = [c for c in panel.columns if 'lag' in c or 'ma' in c or 'diff' in c]
        info("Temporal features added", 
             features=len(temporal_cols),
             types={'lags': len([c for c in temporal_cols if 'lag' in c]),
                    'moving_avg': len([c for c in temporal_cols if 'ma' in c]),
                    'differences': len([c for c in temporal_cols if 'diff' in c])})
        
        return panel
    
    def create_balanced_panel(self, panel: pd.DataFrame, 
                            min_obs_per_entity: int = 12) -> pd.DataFrame:
        """Create balanced panel by filling missing time periods.
        
        Args:
            panel: Input panel DataFrame
            min_obs_per_entity: Minimum observations required per entity
            
        Returns:
            Balanced panel DataFrame
        """
        with timer("create_balanced_panel"):
            info("Creating balanced panel structure", min_obs=min_obs_per_entity)
        
        # Identify panel structure
        if 'commodity' in panel.columns:
            entity_cols = ['commodity', 'market_id']
        else:
            entity_cols = ['market_id']
        
        # Get date range
        date_min = panel['date'].min()
        date_max = panel['date'].max()
        
        # Create all possible combinations
        # For monthly data, WFP uses 15th of each month
        if self.frequency == 'M':
            # Get the day from the actual data (should be 15)
            sample_day = panel['date'].dt.day.mode()[0] if not panel.empty else 15
            all_dates = pd.date_range(
                date_min.replace(day=sample_day), 
                date_max, 
                freq='MS'  # Month start
            )
            # Adjust to the specific day (e.g., 15th)
            all_dates = all_dates + pd.Timedelta(days=sample_day-1)
        else:
            all_dates = pd.date_range(date_min, date_max, freq=self.frequency)
        
        # Get unique entities
        entities = panel[entity_cols].drop_duplicates()
        
        # Create full index
        index_df = pd.MultiIndex.from_product(
            [entities[col].unique() for col in entity_cols] + [all_dates],
            names=entity_cols + ['date']
        ).to_frame(index=False)
        
        # Merge with actual data
        balanced = index_df.merge(
            panel,
            on=entity_cols + ['date'],
            how='left'
        )
        
        # Fill time-invariant variables
        time_invariant = ['market_name', 'governorate', 'district', 'lat', 'lon']
        for col in time_invariant:
            if col in balanced.columns:
                balanced[col] = balanced.groupby(entity_cols)[col].transform(lambda x: x.ffill())
                balanced[col] = balanced.groupby(entity_cols)[col].transform(lambda x: x.bfill())
        
        # Add year_month for consistency
        balanced['year_month'] = balanced['date'].dt.to_period('M')
        
        # Filter entities with sufficient observations
        entity_counts = balanced.groupby(entity_cols).size()
        valid_entities = entity_counts[entity_counts >= min_obs_per_entity].index
        
        if isinstance(valid_entities, pd.MultiIndex):
            mask = balanced.set_index(entity_cols).index.isin(valid_entities)
            balanced = balanced[mask].reset_index(drop=True)
        else:
            balanced = balanced[balanced[entity_cols[0]].isin(valid_entities)]
        
        info("Balanced panel created",
             observations=len(balanced),
             entities=len(valid_entities),
             time_periods=balanced['date'].nunique())
        
        return balanced
    
    def handle_missing_data(self, panel: pd.DataFrame,
                          price_method: str = 'interpolate',
                          rate_method: str = 'forward_fill') -> pd.DataFrame:
        """Handle missing data in panel.
        
        Args:
            panel: Panel DataFrame
            price_method: Method for missing prices ('interpolate', 'forward_fill')
            rate_method: Method for missing exchange rates
            
        Returns:
            Panel with handled missing data
        """
        with timer("handle_missing_data"):
            info("Handling missing data", price_method=price_method, rate_method=rate_method)
        
        # Track missing data
        missing_before = panel.isnull().sum()
        
        # Identify grouping columns
        if 'commodity' in panel.columns:
            group_cols = ['commodity', 'market_id']
        else:
            group_cols = ['market_id']
        
        # Handle prices
        if 'price_usd' in panel.columns:
            if price_method == 'interpolate':
                panel['price_usd'] = panel.groupby(group_cols)['price_usd'].transform(
                    lambda x: x.interpolate(method='linear', limit=2)
                )
            elif price_method == 'forward_fill':
                panel['price_usd'] = panel.groupby(group_cols)['price_usd'].transform(
                    lambda x: x.ffill(limit=2)
                )
        
        # Handle exchange rates
        if 'parallel_rate' in panel.columns:
            if rate_method == 'forward_fill':
                panel['parallel_rate'] = panel.groupby('market_id')['parallel_rate'].transform(
                    lambda x: x.ffill(limit=3)
                )
            
            # Fill remaining with zone average
            if 'control_zone' in panel.columns:
                zone_avg = panel.groupby(['year_month', 'control_zone'])['parallel_rate'].transform('mean')
                panel['parallel_rate'] = panel['parallel_rate'].fillna(zone_avg)
        
        # Handle control zones
        if 'control_zone' in panel.columns:
            # Forward fill control zones (assume persistence)
            panel['control_zone'] = panel.groupby('market_id')['control_zone'].transform(
                lambda x: x.ffill()
            )
            panel['control_zone'] = panel.groupby('market_id')['control_zone'].transform(
                lambda x: x.bfill()
            )
        
        # Report missing data handling
        missing_after = panel.isnull().sum()
        
        missing_summary = {}
        for col in missing_before.index:
            if missing_before[col] > 0:
                missing_summary[col] = {
                    'before': int(missing_before[col]),
                    'after': int(missing_after[col]),
                    'reduction': int(missing_before[col] - missing_after[col])
                }
        
        if missing_summary:
            info("Missing data handled", summary=missing_summary)
        
        return panel
    
    def create_model_specific_panels(self, base_panel: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """Create model-specific panel datasets.
        
        Args:
            base_panel: Base integrated panel
            
        Returns:
            Dictionary of model-specific panels
        """
        with timer("create_model_specific_panels"):
            info("Creating model-specific panel datasets")
        
        panels = {}
        
        # 1. Price transmission panel (market pairs)
        if 'price_usd' in base_panel.columns:
            panels['price_transmission'] = self._create_price_transmission_panel(base_panel)
        
        # 2. Exchange rate pass-through panel
        if 'parallel_rate' in base_panel.columns:
            panels['exchange_passthrough'] = self._create_passthrough_panel(base_panel)
        
        # 3. Threshold cointegration panel
        panels['threshold_coint'] = self._create_threshold_panel(base_panel)
        
        # 4. Spatial panel
        if 'lat' in base_panel.columns and 'lon' in base_panel.columns:
            panels['spatial'] = self._create_spatial_panel(base_panel)
        
        return panels
    
    def _create_price_transmission_panel(self, panel: pd.DataFrame) -> pd.DataFrame:
        """Create panel for price transmission analysis between market pairs."""
        # This would create market-pair observations
        # Simplified version for now
        return panel[panel['commodity'] == 'Wheat'].copy() if 'commodity' in panel.columns else panel
    
    def _create_passthrough_panel(self, panel: pd.DataFrame) -> pd.DataFrame:
        """Create panel for exchange rate pass-through analysis."""
        required_cols = ['price_usd', 'parallel_rate', 'official_rate']
        available_cols = [col for col in required_cols if col in panel.columns]
        
        if not available_cols:
            return pd.DataFrame()
        
        passthrough = panel[available_cols + ['date', 'market_id', 'commodity']].copy()
        
        # Add pass-through ratio
        if 'price_usd_pct_change' in panel.columns and 'parallel_rate_pct_change' in panel.columns:
            passthrough['passthrough_ratio'] = (
                panel['price_usd_pct_change'] / panel['parallel_rate_pct_change']
            ).replace([np.inf, -np.inf], np.nan)
        
        return passthrough
    
    def _create_threshold_panel(self, panel: pd.DataFrame) -> pd.DataFrame:
        """Create panel for threshold cointegration analysis."""
        # Include all necessary variables for threshold models
        threshold_vars = [
            'price_usd', 'price_usd_lag1', 'price_usd_diff',
            'parallel_rate', 'rate_differential',
            'control_zone', 'is_contested', 'time_trend',
            # Conflict variables for regime switching
            'conflict_intensity', 'total_fatalities', 'n_battles',
            'conflict_intensity_lag1', 'conflict_intensity_lag2',
            'conflict_intensity_lag3', 'conflict_ma3'
        ]
        
        available_vars = [v for v in threshold_vars if v in panel.columns]
        base_vars = ['date', 'market_id', 'year_month']
        
        if 'commodity' in panel.columns:
            base_vars.append('commodity')
        
        # Log which conflict variables are available
        conflict_vars_available = [v for v in available_vars if 'conflict' in v or 'fatalities' in v or 'battles' in v]
        if conflict_vars_available:
            info("Threshold panel includes conflict variables", 
                 variables=conflict_vars_available)
        
        return panel[base_vars + available_vars].copy()
    
    def _create_spatial_panel(self, panel: pd.DataFrame) -> pd.DataFrame:
        """Create panel with spatial information."""
        spatial_vars = ['lat', 'lon', 'control_zone', 'distance_to_zone_km']
        available_spatial = [v for v in spatial_vars if v in panel.columns]
        
        return panel[['date', 'market_id'] + available_spatial + ['price_usd']].copy()
    
    def save_panels(self, panels: Dict[str, pd.DataFrame]) -> Dict[str, Path]:
        """Save all panel datasets.
        
        Args:
            panels: Dictionary of panel DataFrames
            
        Returns:
            Dictionary of saved file paths
        """
        saved_files = {}
        
        for panel_name, panel_df in panels.items():
            if panel_df.empty:
                warning(f"Skipping empty panel: {panel_name}")
                continue
            
            # Save as parquet
            parquet_path = self.output_dir / f"{panel_name}_panel.parquet"
            panel_df.to_parquet(parquet_path, index=False)
            saved_files[f'{panel_name}_parquet'] = parquet_path
            
            # Save sample as CSV for inspection
            csv_path = self.output_dir / f"{panel_name}_panel_sample.csv"
            panel_df.head(1000).to_csv(csv_path, index=False)
            saved_files[f'{panel_name}_csv'] = csv_path
            
            info(f"Saved {panel_name} panel", 
                 observations=len(panel_df),
                 path=str(parquet_path))
        
        # Save metadata
        metadata = {
            'created_date': datetime.now().isoformat(),
            'panels': list(panels.keys()),
            'commodities': self.commodities,
            'frequency': self.frequency,
            'observations': {name: len(df) for name, df in panels.items()},
            'date_range': {
                name: {
                    'start': str(df['date'].min()) if 'date' in df.columns else None,
                    'end': str(df['date'].max()) if 'date' in df.columns else None
                }
                for name, df in panels.items() if not df.empty
            }
        }
        
        import json
        metadata_path = self.output_dir / "panel_metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        saved_files['metadata'] = metadata_path
        
        return saved_files
    
    def generate_panel_summary(self, panels: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """Generate summary statistics for all panels.
        
        Args:
            panels: Dictionary of panel DataFrames
            
        Returns:
            Summary statistics DataFrame
        """
        summaries = []
        
        for panel_name, panel_df in panels.items():
            if panel_df.empty:
                continue
            
            summary = {
                'panel': panel_name,
                'n_observations': len(panel_df),
                'n_markets': panel_df['market_id'].nunique() if 'market_id' in panel_df.columns else 0,
                'n_commodities': panel_df['commodity'].nunique() if 'commodity' in panel_df.columns else 1,
                'date_range': f"{panel_df['date'].min()} to {panel_df['date'].max()}" if 'date' in panel_df.columns else 'N/A',
                'missing_price_pct': (panel_df['price_usd'].isnull().sum() / len(panel_df) * 100) if 'price_usd' in panel_df.columns else 0,
                'n_control_zones': panel_df['control_zone'].nunique() if 'control_zone' in panel_df.columns else 0,
                'has_conflict_data': 'conflict_intensity' in panel_df.columns,
                'avg_conflict_intensity': panel_df['conflict_intensity'].mean() if 'conflict_intensity' in panel_df.columns else None,
                'conflict_coverage_pct': (panel_df['conflict_intensity'].notna().sum() / len(panel_df) * 100) if 'conflict_intensity' in panel_df.columns else 0
            }
            
            summaries.append(summary)
        
        return pd.DataFrame(summaries)
    
    def _derive_exchange_rates_from_prices(self, data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        Derive implied exchange rates from dual-currency price data.
        
        This method implements World Bank methodology for inferring exchange rates
        from commodity prices when direct exchange rate data is unavailable.
        Uses the law of one price and purchasing power parity principles.
        
        Parameters
        ----------
        data : dict
            Dictionary containing price data
            
        Returns
        -------
        pd.DataFrame
            Derived exchange rate panel
        """
        from yemen_market.utils.logging import info, warning
        
        info("Deriving exchange rates from commodity price data")
        
        if 'prices' not in data:
            warning("No price data available to derive exchange rates")
            return pd.DataFrame()
        
        prices = data['prices'].copy()
        
        # Check if we have both USD and YER prices
        if 'price_usd' not in prices.columns or 'price_yer' not in prices.columns:
            warning("Need both USD and YER prices to derive exchange rates")
            return pd.DataFrame()
        
        # Calculate implied exchange rate for each observation
        prices['implied_exchange_rate'] = prices['price_yer'] / prices['price_usd']
        
        # Remove outliers using IQR method
        Q1 = prices['implied_exchange_rate'].quantile(0.25)
        Q3 = prices['implied_exchange_rate'].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        prices = prices[
            (prices['implied_exchange_rate'] >= lower_bound) & 
            (prices['implied_exchange_rate'] <= upper_bound)
        ]
        
        # Aggregate by market and time period
        if 'date' in prices.columns:
            prices['year_month'] = pd.to_datetime(prices['date']).dt.to_period('M')
        
        # Use tradable commodities (less affected by local factors)
        tradable_commodities = ['Wheat', 'Rice', 'Sugar', 'Cooking oil']
        tradable_mask = prices['commodity'].isin(tradable_commodities)
        
        # Calculate market-level exchange rates
        exchange_rates = prices[tradable_mask].groupby(['market_id', 'year_month']).agg({
            'implied_exchange_rate': ['mean', 'std', 'count']
        }).reset_index()
        
        exchange_rates.columns = ['market_id', 'year_month', 'exchange_rate', 'rate_std', 'n_obs']
        
        # Add control zone information if available
        if 'market_zones' in data:
            zones = data['market_zones'].copy()
            if 'year_month' not in zones.columns and 'date' in zones.columns:
                zones['year_month'] = pd.to_datetime(zones['date']).dt.to_period('M')
            
            exchange_rates = exchange_rates.merge(
                zones[['market_id', 'year_month', 'control_zone']].drop_duplicates(),
                on=['market_id', 'year_month'],
                how='left'
            )
        
        # Calculate zone-level rates and differentials
        if 'control_zone' in exchange_rates.columns:
            zone_rates = exchange_rates.groupby(['year_month', 'control_zone'])['exchange_rate'].agg([
                'mean', 'std', 'count'
            ]).reset_index()
            
            zone_rates.columns = ['year_month', 'control_zone', 'zone_rate', 'zone_std', 'n_markets']
            
            # Calculate dual exchange rate indicators
            pivot_rates = zone_rates.pivot(
                index='year_month',
                columns='control_zone',
                values='zone_rate'
            ).reset_index()
            
            # Calculate differentials for all zone pairs
            zone_names = [col for col in pivot_rates.columns if col != 'year_month']
            
            for i, zone1 in enumerate(zone_names):
                for zone2 in zone_names[i+1:]:
                    if zone1 in pivot_rates.columns and zone2 in pivot_rates.columns:
                        diff_col = f'{zone1}_{zone2}_differential'
                        pct_col = f'{zone1}_{zone2}_differential_pct'
                        
                        pivot_rates[diff_col] = pivot_rates[zone1] - pivot_rates[zone2]
                        pivot_rates[pct_col] = (pivot_rates[diff_col] / pivot_rates[zone2]) * 100
            
            # Merge back differentials
            exchange_rates = exchange_rates.merge(
                pivot_rates,
                on='year_month',
                how='left'
            )
            
            # Add volatility measures
            exchange_rates['rate_volatility'] = exchange_rates.groupby('market_id')['exchange_rate'].transform(
                lambda x: x.rolling(window=3, min_periods=1).std()
            )
            
            # Add persistence measure (AR(1) coefficient proxy)
            exchange_rates['rate_persistence'] = exchange_rates.groupby('market_id')['exchange_rate'].transform(
                lambda x: x.autocorr(lag=1) if len(x) > 1 else np.nan
            )
        
        # Add metadata
        exchange_rates['source'] = 'derived_from_prices'
        exchange_rates['derivation_method'] = 'tradable_commodities_ppp'
        
        info(f"Derived exchange rates for {len(exchange_rates)} market-month observations")
        
        return exchange_rates
"""
Main Orchestrator for Three-Tier Panel Diagnostics.

This module defines the `ThreeTierPanelDiagnostics` class, which is responsible
for running diagnostic tests on results from the three-tier models.
"""

from typing import Any, Dict, Optional
import pandas as pd

from yemen_market.models.three_tier.core.results_container import ResultsContainer
from yemen_market.utils.logging import bind, info, warning, error
from .diagnostic_adapters import DiagnosticAdapter
from .test_implementations import (
    wooldridge_serial_correlation,
    pesaran_cd_test,
    ips_unit_root_test,
    modified_wald_heteroskedasticity,
    breusch_pagan_lm_test,
)
from .diagnostic_reports import DiagnosticReport


class ThreeTierPanelDiagnostics:
    """
    Orchestrates diagnostic testing for three-tier panel models.

    This class works with the `ResultsContainer` interface, runs tier-specific
    diagnostic batteries, stores results back in the `ResultsContainer`, and
    can generate publication-ready reports.
    """

    def __init__(self, tier: int, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the diagnostic suite for a specific tier.

        Parameters
        ----------
        tier : int
            The tier number (1, 2, or 3) for which diagnostics are being run.
        config : Optional[Dict[str, Any]], optional
            Configuration settings for the diagnostic tests, by default None.
        """
        self.tier = tier
        self.config = config or {}
        self.adapter: Optional[DiagnosticAdapter] = None
        self.diagnostic_report: Optional[DiagnosticReport] = None

        bind(module=f"ThreeTierPanelDiagnostics_Tier{tier}")
        info(f"Initialized ThreeTierPanelDiagnostics for Tier {tier}")

    def run_diagnostics(
        self,
        results_container: ResultsContainer,
        data: pd.DataFrame,
        diagnostic_config: Optional[Dict[str, Any]] = None
    ) -> DiagnosticReport:
        """
        Run a battery of diagnostic tests appropriate for the specified tier.

        Parameters
        ----------
        results_container : ResultsContainer
            The container holding the model estimation results.
        data : pd.DataFrame
            The input data used for the model estimation.
            This may be needed for some tests that require original data.
        diagnostic_config : Optional[Dict[str, Any]], optional
            Specific configuration for this run of diagnostics, by default None.
            This can override or augment the instance's config.

        Returns
        -------
        DiagnosticReport
            A report object containing all test results and interpretations.
        """
        info(f"Running diagnostics for Tier {self.tier}...")
        self.config.update(diagnostic_config or {})
        self.adapter = DiagnosticAdapter(results_container, data, self.tier)
        self.diagnostic_report = DiagnosticReport(tier=self.tier)

        # Extract necessary data from the adapter
        residuals = self.adapter.get_residuals()
        panel_info = self.adapter.get_panel_structure()
        fitted_values = self.adapter.get_fitted_values()
        
        if residuals is None or panel_info is None:
            error("Could not retrieve residuals or panel information. Diagnostics aborted.")
            if self.diagnostic_report:
                 self.diagnostic_report.add_critical_error(
                    "Data Retrieval Error",
                    "Failed to get residuals or panel structure from ResultsContainer."
                 )
            return self.diagnostic_report
        
        # Determine which tests to run based on tier and configuration
        tests_to_run = self._determine_tests_to_run()

        # --- 1. Wooldridge Test for Serial Correlation (Critical for panels) ---
        if "serial_correlation" in tests_to_run:
            try:
                wd_stat, wd_p_value, wd_recommendation = wooldridge_serial_correlation(
                    residuals, panel_info
                )
                self.diagnostic_report.add_test_result(
                    test_name="Wooldridge Test for Serial Correlation",
                    statistic=wd_stat,
                    p_value=wd_p_value,
                    interpretation=f"P-value: {wd_p_value:.4f}. {wd_recommendation}",
                    passed=wd_p_value > 0.05, # Assuming null is no serial correlation
                    recommendation=wd_recommendation
                )
            except Exception as e:
                error(f"Error running Wooldridge test: {e}")
                self.diagnostic_report.add_test_error("Wooldridge Test", str(e))


        # --- 2. Modified Wald Test for Groupwise Heteroskedasticity (Critical for FE models) ---
        if "heteroskedasticity" in tests_to_run:
            try:
                mw_stat, mw_p_value, mw_recommendation = modified_wald_heteroskedasticity(
                    residuals, panel_info, fitted_values
                )
                self.diagnostic_report.add_test_result(
                    test_name="Modified Wald Test for Groupwise Heteroskedasticity",
                    statistic=mw_stat,
                    p_value=mw_p_value,
                    interpretation=f"Chi2 statistic: {mw_stat:.4f}, P-value: {mw_p_value:.4f}. {mw_recommendation}",
                    passed=mw_p_value > 0.05,  # Null is homoskedasticity
                    recommendation=mw_recommendation
                )
            except Exception as e:
                error(f"Error running Modified Wald test: {e}")
                self.diagnostic_report.add_test_error("Modified Wald Test", str(e))
        
        # --- 3. Cross-Sectional Dependence Tests ---
        if "cross_sectional_dependence" in tests_to_run:
            # Run Pesaran CD Test
            if self.config.get("run_pesaran_cd_test", True):
                try:
                    cd_stat, cd_p_value, cd_recommendation = pesaran_cd_test(
                        residuals, panel_info
                    )
                    self.diagnostic_report.add_test_result(
                        test_name="Pesaran CD Test for Cross-Sectional Dependence",
                        statistic=cd_stat,
                        p_value=cd_p_value,
                        interpretation=f"P-value: {cd_p_value:.4f}. {cd_recommendation}",
                        passed=cd_p_value > 0.05, # Assuming null is cross-sectional independence
                        recommendation=cd_recommendation
                    )
                except Exception as e:
                    error(f"Error running Pesaran CD test: {e}")
                    self.diagnostic_report.add_test_error("Pesaran CD Test", str(e))
            
            # Run Breusch-Pagan LM Test (alternative/complement to Pesaran CD)
            if self.config.get("run_breusch_pagan_test", True):
                try:
                    bp_stat, bp_p_value, bp_recommendation = breusch_pagan_lm_test(
                        residuals, panel_info, scaled=True
                    )
                    self.diagnostic_report.add_test_result(
                        test_name="Breusch-Pagan LM Test for Cross-Sectional Dependence",
                        statistic=bp_stat,
                        p_value=bp_p_value,
                        interpretation=f"P-value: {bp_p_value:.4f}. {bp_recommendation}",
                        passed=bp_p_value > 0.05,  # Null is no cross-sectional dependence
                        recommendation=bp_recommendation
                    )
                except Exception as e:
                    error(f"Error running Breusch-Pagan LM test: {e}")
                    self.diagnostic_report.add_test_error("Breusch-Pagan LM Test", str(e))

        # --- 4. Panel Unit Root Tests ---
        if "unit_root" in tests_to_run:
            target_series_name = self.config.get("ips_target_series", "price_usd") # Example
            series_to_test = self.adapter.get_series(target_series_name)
            if series_to_test is not None:
                try:
                    ips_stat, ips_p_value, ips_recommendation = ips_unit_root_test(
                        series_to_test, panel_info
                    )
                    self.diagnostic_report.add_test_result(
                        test_name=f"Im-Pesaran-Shin Unit Root Test (on {target_series_name})",
                        statistic=ips_stat,
                        p_value=ips_p_value,
                        interpretation=f"P-value: {ips_p_value:.4f}. {ips_recommendation}",
                        passed=ips_p_value < 0.05, # Assuming null is unit root
                        recommendation=ips_recommendation
                    )
                except Exception as e:
                    error(f"Error running IPS unit root test: {e}")
                    self.diagnostic_report.add_test_error("IPS Unit Root Test", str(e))
            else:
                warning(f"Could not retrieve series '{target_series_name}' for IPS test.")


        # Store diagnostic report in ResultsContainer (if applicable)
        # This part might need adjustment based on ResultsContainer's API
        if hasattr(results_container, 'add_diagnostics_report'):
            results_container.add_diagnostics_report(self.diagnostic_report)
        elif hasattr(results_container, 'diagnostics'):
            results_container.diagnostics = self.diagnostic_report.to_dict()
        else:
            warning("ResultsContainer does not have a standard method to store diagnostics.")

        # Check for critical failures and provide summary
        if self.diagnostic_report.has_critical_failures():
            warning(f"Critical diagnostic failures detected for Tier {self.tier}")
            self._log_diagnostic_summary()
        
        info(f"Diagnostics for Tier {self.tier} completed.")
        return self.diagnostic_report
    
    def _determine_tests_to_run(self) -> set:
        """Determine which tests to run based on tier and configuration."""
        # Default tests for all tiers
        default_tests = {"serial_correlation", "heteroskedasticity", "cross_sectional_dependence"}
        
        # Tier-specific adjustments
        if self.tier == 1:
            # Pooled models need all tests
            tests = default_tests.copy()
            if self.config.get("check_unit_roots", True):
                tests.add("unit_root")
        elif self.tier == 2:
            # Commodity-specific models
            tests = default_tests.copy()
            # Unit root tests less critical for commodity-specific models
            if self.config.get("check_unit_roots", False):
                tests.add("unit_root")
        else:  # Tier 3
            # Market-pair models might have different needs
            tests = {"serial_correlation", "heteroskedasticity"}
            # Cross-sectional dependence less relevant for market-pair models
            if self.config.get("check_cross_sectional", False):
                tests.add("cross_sectional_dependence")
        
        # Allow override from config
        if "tests_to_run" in self.config:
            tests = set(self.config["tests_to_run"])
        
        info(f"Tests to run for Tier {self.tier}: {tests}")
        return tests
    
    def _log_diagnostic_summary(self) -> None:
        """Log a summary of diagnostic results."""
        if not self.diagnostic_report:
            return
        
        summary = self.diagnostic_report.get_summary()
        info("=== Diagnostic Summary ===")
        info(f"Total tests run: {summary['total_tests']}")
        info(f"Tests passed: {summary['passed_tests']}")
        info(f"Tests failed: {summary['failed_tests']}")
        info(f"Critical failures: {summary['critical_failures']}")
        
        if summary['failed_tests'] > 0:
            info("Failed tests:")
            for test_name, result in self.diagnostic_report.test_results.items():
                if not result.get('passed', True):
                    info(f"  - {test_name}: p-value = {result.get('p_value', 'N/A')}")

    def _apply_diagnostic_corrections(
        self,
        model: Any,
        diag_report: DiagnosticReport
    ) -> Dict[str, Any]:
        """
        Apply automatic corrections to the model based on diagnostic failures.
        
        Returns a dictionary of corrections applied.
        """
        corrections = {}
        
        if not diag_report.has_critical_failures():
            return corrections
        
        info("Applying automatic corrections based on diagnostic failures...")
        
        # Check for serial correlation
        wooldridge_result = diag_report.test_results.get("Wooldridge Test for Serial Correlation")
        if wooldridge_result and not wooldridge_result.get('passed', True):
            info("Serial correlation detected - recommending cluster-robust or Driscoll-Kraay SEs")
            corrections['standard_errors'] = 'driscoll_kraay'
            corrections['serial_correlation_detected'] = True
        
        # Check for heteroskedasticity
        mw_result = diag_report.test_results.get("Modified Wald Test for Groupwise Heteroskedasticity")
        if mw_result and not mw_result.get('passed', True):
            info("Groupwise heteroskedasticity detected - recommending cluster-robust SEs")
            if 'standard_errors' not in corrections:
                corrections['standard_errors'] = 'cluster_robust'
            corrections['heteroskedasticity_detected'] = True
        
        # Check for cross-sectional dependence
        cd_result = diag_report.test_results.get("Pesaran CD Test for Cross-Sectional Dependence")
        if cd_result and not cd_result.get('passed', True):
            info("Cross-sectional dependence detected - Driscoll-Kraay SEs strongly recommended")
            corrections['standard_errors'] = 'driscoll_kraay'
            corrections['cross_sectional_dependence_detected'] = True
        
        # Check for unit roots
        ips_result = diag_report.test_results.get("Im-Pesaran-Shin Unit Root Test")
        if ips_result and not ips_result.get('passed', True):
            info("Unit roots detected - recommending first-differencing or cointegration methods")
            corrections['transformation'] = 'first_difference'
            corrections['unit_roots_detected'] = True
        
        return corrections
